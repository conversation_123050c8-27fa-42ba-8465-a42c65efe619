<template>
  <a-drawer :title="configdata.type == '1' ? title : ''" :width="drawerWidth" @close="handleCancel" :visible="visible"
    :confirmLoading="confirmLoading" :closable="configdata.type == 1 ? true : false">
    <div :style="{ width: '100%', background: '#fff' }" class="pb60">
      <template v-if="configdata.type == 1">
        <a-spin :spinning="confirmLoading">
          <a-form :form="form">
            <a-form-item label="账户名称" :labelCol="labelCol" :wrapperCol="wrapperCol" hasFeedback required>
              <a-input :disabled="record.way == 1" placeholder="请输入账户名称" v-decorator="[
                'sub_advertiser_name',
                validatorRules.sub_advertiser_name
              ]" :readOnly="disableSubmit" :maxLength="50" />
            </a-form-item>
            <!-- <a-form-item label="编码" :labelCol="labelCol" :wrapperCol="wrapperCol" hasFeedback required>
            <a-input placeholder="请输入编码" v-decorator="['code', validatorRules.code]" :readOnly="disableSubmit" />
            </a-form-item>-->
            <a-form-item label="推广渠道" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-select show-search :disabled="record.way == 1" @search="e => handleSearch(e, 'channelList')"
                placeholder="请选择渠道" v-decorator="['promote_id', validatorRules.promote_id]" style="width: 100%"
                :default-active-first-option="false" :show-arrow="false" :filter-option="false"
                :not-found-content="null">
                <a-select-option :key="item.id" :value="item.id" v-for="item in channelList">
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="负责人" :labelCol="labelCol" :wrapperCol="wrapperCol" hasFeedback required>
              <a-select show-search @search="e => handleSearch(e, 'personCharge', 'promotePerson')" placeholder="请选择负责人"
                v-decorator="['responsible_id', validatorRules.responsible_id]" ref="store" style="width: 100%"
                :default-active-first-option="false" :show-arrow="false" :filter-option="false"
                :not-found-content="null">
                <a-select-option :key="item.id" :value="item.id" v-for="item in personCharge">
                  {{ item.username }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="定向" :labelCol="labelCol" :wrapperCol="wrapperCol" hasFeedback>
              <commone-self-principal searchKey="keyword" :requestFun="directionSelect" placeholder="请选择定向"
                :disableOption="true" :defaultRendering="directiontReserved" value_key="name" :showAll="false"
                v-decorator="['direction_id', validatorRules.direction_id]" />
            </a-form-item>

            <a-form-item label="链路" :labelCol="labelCol" :wrapperCol="wrapperCol" hasFeedback>
              <a-select placeholder="请选择链路" v-decorator="['link_id', validatorRules.link_id]">
                <a-select-option :key="item.id" :value="item.id" v-for="item in linkList">
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="项目" :labelCol="labelCol" :wrapperCol="wrapperCol" hasFeedback>
              <a-select placeholder="请选择项目" v-decorator="['project_id', validatorRules.project_id]">
                <a-select-option :key="item.id" :value="item.id" v-for="item in projectList">
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="推广代号" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <bisForSelect ref="codeList" style="width: 320px" :isrequest="false" :hiddenTab="true"
                v-decorator="['code', validatorRules.code]" />
            </a-form-item>



            <!-- <a-form-item label="代理商" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="record.id == 0">
            <a-select
              show-search
              @search="(e) => handleSearch(e, 'filterListData', 'filterList')"
              placeholder="选择代理商"
              v-decorator="['agent_id', validatorRules.agent_id]"
              ref="store"
              style="width: 100%"
              :default-active-first-option="false"
              :show-arrow="false"
              :filter-option="false"
              :not-found-content="null"
            >
              <a-select-option :key="item.id" :value="item.id" v-for="item in filterListData">{{
                item.name
              }}</a-select-option>
            </a-select>
            </a-form-item>-->









            <!-- <a-form-item
              label="重粉回传比例"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              hasFeedback
              required
            >
              <a-input-number
                placeholder="请输入重粉回传比例"
                v-decorator="[
                  'heavy_powder_rate',
                  {
                    initialValue: model.heavy_powder_rate ? Number(model.heavy_powder_rate) : undefined,
                    rules: [
                      { required: true, message: '请输入重粉回传比例!' },
                      { type: 'number', min: 1, max: 10, message: '重粉回传比例必须在1-10之间!' }
                    ]
                  }
                ]"
                style="width: 50%"
                :min="1"
                :max="10"
              />
            </a-form-item> -->

            <!-- <a-form-item
            label="返点"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            hasFeedback
            required
            v-if="record.id == 0"
          >
            <a-input-number
              style="width: 100%"
              placeholder="请输入返点"
              v-decorator="['rebates', validatorRules.rebates]"
              :readOnly="disableSubmit"
              :precision="2"
            />
            </a-form-item>-->
          </a-form>
        </a-spin>
      </template>
      <template v-else>
        <div class="planTopBox">
          <div style="font-size: 18px">{{ record.sub_advertiser_name }}</div>
        </div>
        <div class="planTopSeach">
          <div class="planTopSeach-title">计划名称</div>
          <div class="planTopSeach-input">
            <a-input-search placeholder="请输入计划名称" enter-button="查询" size="default" @search="onSeach" />
          </div>
          <a href="javascript:;" @click="onSeach('')">重置查询</a>
        </div>
        <a-table ref="table" size="middle" rowKey="id" :columns="columns" :dataSource="dataSourceFormat"
          :pagination="ipagination" :loading="loading" @change="tableChange"></a-table>
      </template>
      <a-modal width="552px" v-model="visiblsdsde" title="选择日期" :bodyStyle="{ padding: 0 }" ref="pickerModal">
        <a-range-picker open AutoFocus show-time :ranges="{
          今天: [moment(), moment()],
          今月: [moment(), moment().endOf('month')]
        }" @ok="pickerChange" format="YYYY-MM-DD" :getCalendarContainer="triggerNode => triggerNode.parentNode" />
      </a-modal>
      <div class="right-side-bottom-bar">
        <a-button size="large" class="pl25 pr25 mr15" @click="handleCancel">关闭</a-button>
        <a-button size="large" class="pl25 pr25" :disabled="disableSubmit" @click="handleOk" type="primary"
          v-if="configdata.type == 1">确定</a-button>
        <a-button size="large" class="pl25 pr25" :disabled="disableSubmit" @click="updatePlan" type="primary"
          v-else>更新计划信息</a-button>
      </div>
    </div>
  </a-drawer>
</template>

<script>
import { promoteManageAccount, promoteManageLocalAccount, promoteAccount, direction, bindCode } from "@/api/api";
import pick from "lodash.pick";
import JImageUpload from "@/components/jeecg/JImageUpload";
import commentMixin from "@/mixins/commentMixin";
import moment from "moment";
import bisForSelect from "@/views/serviceWorkbench/orderList/bisForSelect";
export default {
  name: "MaterialModal",
  components: { JImageUpload, bisForSelect },
  mixins: [commentMixin],
  data() {
    return {
      moment,
      directionSelect: direction.select,
      directiontReserved: {},
      visiblsdsde: false,
      drawerWidth: 700,
      treeData: [],
      configdata: {},
      title: "操作",
      visible: false,
      redirect: "",
      disableSubmit: false,
      model: {},
      request_way: "GET",
      show: true, //根据菜单类型，动态显示隐藏表单元素
      menuLabel: "接口名称",
      isRequrie: true, // 是否需要验证
      rtype: "1", //手动输入/选中已有
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },

      columns: [
        {
          title: "计划ID",
          align: "center",
          dataIndex: "campaign_id",
          scopedSlots: { customRender: "campaign_id" }
        },
        {
          title: "计划名称",
          align: "center",
          dataIndex: "campaign_name",
          scopedSlots: { customRender: "campaign_name" }
        },
        {
          title: "更新时间",
          align: "center",
          dataIndex: "updated_at",
          scopedSlots: { customRender: "updated_at" }
        }
      ],
      dataSourceFormat: [],
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 10,
        limit: 10,
        showTotal: function (total, range) {
          let page = range[1] + "/页 共" + total + "条";
          return page;
        }
      },
      loading: false,

      confirmLoading: false,
      form: this.$form.createForm(this),
      iconChooseVisible: false,
      validateStatus: "",
      searchSelect: {
        data: [],
        value: undefined
      },
      detail: undefined,
      linkList: [], // 链路列表
      channelList: [], // 渠道列表
      projectList: [], // 项目列表
      personCharge: [], // 负责人
      filterListData: [], // 代理商
      isCreate: false,
      test: ["测试账户1", "测试账户2"],
      timeid: null, // 防抖
      record: {},
      temporary: {},
      name: "",
      planId: ""
    };
  },
  computed: {
    validatorRules: function () {
      return {
        sub_advertiser_name: {
          rules: [{ required: true, message: "请输入账户名!" }]
        }, // 账号名称
        promote_id: { rules: [{ required: true, message: "请选择推广渠道!" }] }, // 渠道ID
        responsible_id: {
          rules: [{ required: true, message: "请选择负责人!" }]
        }, // 负责人ID
        link_id: { rules: [{ required: true, message: "请选择链路!" }] }, // 链路ID
        project_id: { rules: [{ required: true, message: "请选择项目!" }] }, // 项目ID
        direction_id: { rules: [{ required: true, message: "请选择定向!" }] }, // 项目ID
        code: { rules: [{ required: true, message: "请选择推广代号!" }] }, // 推广代号
        // rebates: { rules: [{ required: true, message: '请输返点!' }] }, // 返点 保留两位 小数
        rebates: {
          // 金额   必填 数字 限制大小  小数点后只能两位小数
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (!value) {
                  if (value == 0) {
                    cbfn("返点不能小于等于0！");
                  } else {
                    cbfn("请输入返点！");
                  }
                } else if (value <= 0) {
                  cbfn("返点不能小于等于0！");
                }
                cbfn();
              }
            }
          ]
        },
        agent_id: { rules: [{ required: true, message: "请选择代理商ID!" }] } // 代理商ID
      };
    }
  },
  created() {
    this.promotePerson();
  },
  methods: {
    pickerChange(e) {
      const _this = this;
      const h = this.$createElement;
      // this.visiblsdsde = false
      // let date = {
      //   pull_time_start: e[0] ? moment(e[0]).format('YYYY-MM-DD') : '',
      //   pull_time_end: e[1] ? moment(e[1]).format('YYYY-MM-DD') : '',
      // }
      this.$confirm({
        title: "提示",
        content: h("div", {}, [
          h("div", {}, "请确认更新计划")
          // h('div', { style: { color: '#ccc' } }, this.record.sub_advertiser_name),
          // h('div', { style: { color: '#ccc' } }, '计划时间：' + date.pull_time_start + '-' + date.pull_time_end),
        ]),
        onOk() {
          _this.disableSubmit = true;
          promoteManageAccount
            .updateProgram({ ads_sub_id: _this.record.id })
            .then(res => {
              console.log(res);
              _this.disableSubmit = false;
              _this.Plan();
              if (res.code == 200) _this.$message.success(res.message);
              else _this.$message.error(res.message);
            });
        }
      });
    },
    getCategory(id) {
      return new Promise((resolve, reject) => {
        promoteManageLocalAccount
          .view({ id })
          .then(res => {
            console.log("getCategory", res);
            resolve(res);
          })
          .catch(err => {
            console.log(err);
            reject(err);
          });
      });
    },
    filterList(keyword = "") {
      return new Promise((resolve, reject) => {
        promoteManageAccount
          .filterList({ keyword })
          .then(res => {
            console.log("filterList", res);
            resolve(res);
          })
          .catch(err => {
            console.log(err);
            reject(err);
          });
      });
    },
    promotePerson(username = "") {
      return new Promise((resolve, reject) => {
        promoteAccount
          .getPromotePerson({ username })
          .then(res => {
            resolve(res);
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    updatePlan() {
      console.log("更新计划");
      this.pickerChange();
      // this.visiblsdsde = true
    },
    add() {
      // 默认值
      this.edit({ id: 0 });
      this.isCreate = true;
    },
    async edit(record) {
      this.resetScreenSize(); // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
      this.form.resetFields();
      this.record = JSON.parse(JSON.stringify(record));

      if (this.configdata.type == 1) {
        this.account(record);
      } else {
        this.Plan();
      }
    },
    //  广告计划数据回显
    tableChange({ current }) {
      this.ipagination.current = current;
      this.Plan();
    },
    onSeach(name = "") {
      this.name = name;
      this.Plan();
    },
    async Plan() {
      this.loading = true;
      let data = {
        ads_sub_id: this.record.id,
        name: this.name,
        page: this.ipagination.current,
        limit: this.ipagination.pageSize
      };
      promoteManageAccount.getPlanView(data).then(res => {
        this.dataSourceFormat = res.data.list;
        this.ipagination.total = parseInt(res.data.totalCount);
        this.loading = false;
      });
      this.visible = true;
    },
    //  账户数据回显
    async account(record) {
      let filterList = await this.filterList();
      let promotePerson = await this.promotePerson();
      this.filterListData = filterList.data;
      this.personCharge = promotePerson.data;
      let {
        code,
        data: { channelList, projectList, linkList, info }
      } = await this.getCategory(record.id);
      if (code != "200") return;

      this.model = Object.assign({}, info);
      if (info.responsible_id && !this.personCharge.some(item => item.id == info.responsible_id)) {
        this.personCharge.push({
          id: info.responsible_id,
          username: info.responsible_name
        });
      }
      this.linkList = await linkList;
      this.channelList = channelList;
      this.projectList = await projectList;
      console.log(this.channelList);
      console.log("filterListData:::::", this.filterListData);
      let keyArr = ["link_id", "project_id"];
      for (let i = 0; i < keyArr.length; i++) {
        let key = keyArr[i];
        if (this.model[key] == "0") {
          this.model[key] = undefined;
        } else {
          this.model[key] =
            this.model[key] != "" && this.model[key]
              ? String(this.model[key])
              : undefined;
        }
      }
      this.visible = true;
      // 安全地转换布尔值，新建时设置默认值
      console.log("model", this.model);
      if (typeof this.model.promote_id == "number") {
        if (this.model.promote_id == 0) {
          this.model.promote_id = undefined;
        } else {
          this.model.promote_id = this.model.promote_id.toString();
        }
      }
      if (typeof this.model.responsible_id == "number") {
        if (this.model.responsible_id == 0) {
          this.model.responsible_id = undefined;
        } else {
          this.model.responsible_id = this.model.responsible_id.toString();
        }
      }
      let modeldefaltUdefin = ["direction_id"];
      modeldefaltUdefin.forEach(key => {
        if (!this.model[key]) this.model[key] = undefined;
        if (this.model[key] === "0") this.model[key] = undefined;
      });
      if (this.model.direction_id && this.model.direction_id != "0") {
        this.directiontReserved = {
          id: this.model.direction_id,
          name: this.model.direction_name,
          status: "1"
        };
      }

      let fieldsVal = pick(
        this.model,
        "sub_advertiser_name",
        "promote_id",
        "responsible_id",
        "link_id",
        "project_id",
        "direction_id",
        "operation_time"
      );

      // 处理推广代号数据回显，需要转换为bisForSelect组件期望的格式
      if (this.model.code) {
        // 先设置一个临时的对象，在组件打开时会重新获取完整信息
        fieldsVal.code = {
          id: this.model.code, // 临时使用code作为id
          code: this.model.code
        };
      }

      this.$nextTick(() => {
        this.form.setFieldsValue(fieldsVal);
      });
    },
    close() {
      this.$emit("close");
      this.disableSubmit = false;
      this.visible = false;
      this.isCreate = false;
      this.searchSelect = {
        data: [],
        value: undefined
      };
    },
    requestFun() {
      const that = this;
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          let formData = Object.assign(this.model, values);
          console.log(
            "values",
            values,
            "modal",
            this.model,
            "formdata",
            formData
          );

          // 处理推广代号数据格式，参考参考页面的处理方式
          if (values.code && values.code.code) {
            formData.code = values.code.code;
          }

          // formData.tag=formData.tag.toString().replace(/,/g,"-");
          //  formData.operation_time = parseInt(new Date(formData.operation_time).getTime()/1000).toString();
          // return
          formData.supplier_id = this.searchSelect.value;
          that.confirmLoading = true;
          let obj;
          that.disableSubmit = true;
          if (!this.model.id) {
            //新增
            console.log("新增！", formData);
            delete formData.id;

            obj = promoteManageLocalAccount.create(formData);
            console.log("obj", obj);
          } else {
            //修改
            obj = promoteManageLocalAccount.update(formData);
          }
          obj
            .then(res => {
              if (res.code == "200") {
                that.$message.success(res.message);
                that.$emit("ok");
                that.close();
              } else {
                that.$message.warning(res.message);
              }
              that.disableSubmit = false;
            })
            .finally(() => {
              that.confirmLoading = false;
              that.disableSubmit = false;
            });
        }
      });
    },
    handleCancel() {
      this.close();
    },
    handleChange(value) {
      console.log(value);
      this.searchSelect.value = value;
      fetch(this.currentEntity, value, data => (this.searchSelect.data = data));
      this.queryParam.dept_id = this.searchSelect.value;
      this.searchQuery();
    },

    /**
     * 根据推广代号字符串获取完整的推广代号信息
     */
    async getCodeInfoByCode(codeStr) {
      try {
        const res = await bindCode.matchCode({ keyword: codeStr, is_bound: 1 });
        if (res.code === 200 && res.data && res.data.list) {
          // 查找匹配的推广代号
          const codeInfo = res.data.list.find(item => item.code === codeStr);
          return codeInfo || null;
        }
        return null;
      } catch (error) {
        console.error('获取推广代号信息失败:', error);
        return null;
      }
    },
    /**
     * slesct下拉树 数据格式转换
     *@param  treeList 需转换的数组
     */
    treeDataformat(treeList, hierarchy) {
      let arr = [];
      let that = this;
      for (let a = 0; a < treeList.length; a++) {
        let temp = treeList[a];
        // if(temp.id==0){
        //   continue;
        // }
        let id = null;
        if (hierarchy == 1) {
          id = "h1" + a;
        } else {
          id = temp.id;
        }
        let obj = {
          label: temp.name,
          value: id,
          hierarchy
        };

        if (temp.child && temp.child.length) {
          obj.children = this.treeDataformat(temp.child, 2);
        }
        arr.push(obj);
      }
      // console.log('arr',arr);
      return arr;
    },
    // 根据屏幕变化,设置抽屉尺寸
    resetScreenSize() {
      let screenWidth = document.body.clientWidth;
      if (screenWidth < 500) {
        this.drawerWidth = screenWidth;
      } else {
        this.drawerWidth = 600;
      }
    },
    handleTypeChose(value) {
      this.request_way = value;
      console.log(value);
    },
    handleParentIdChange(e, label, extra, type) {
      console.log(this.$refs.treeSelect);
      // this.$refs.treeSelect.$vnode.elm.hidden = true
      if (!e) {
        this.validateStatus = "error";
      } else {
        this.validateStatus = "success";
        if (e.indexOf("h1") > -1) {
          let fieldsVal = pick({ promote_id: undefined }, "promote_id");
          this.$nextTick(() => {
            this.form.setFieldsValue(fieldsVal);
          });
        }
      }
    },
    create69() {
      let code = new Date().getTime();
      console.log("code", code);
      this.form.setFieldsValue({ bar_code: code });
    }
  }
};
</script>

<style lang="less">
.ant-calendar-picker-container-placement-bottomLeft {
  z-index: 999999;
}

.planTopBox {
  padding-top: 10px;

  >div:nth-child(1) {
    font-weight: 500;
  }
}

.planTopSeach {
  &-input {
    width: 280px;
  }

  &-title {
    width: 65px;
  }

  padding: 20px 15px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: #fafafa;
  margin: 10px 0;
  border-radius: 4px;

  a {
    width: 100px;
    margin-left: 20px;
  }

  /deep/ .ant-btn {
    margin: 0 !important;
  }
}
</style>
