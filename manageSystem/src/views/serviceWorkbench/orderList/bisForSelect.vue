<template>
  <div>
    <div @click="openTag" v-if="showSelect">
      <a-select v-model="selectCode.id" :notFoundContent="null" :dropdownStyle="{ maxHeight: '0', overflow: 'hidden' }"
        placeholder="请选择推广代号" :disabled="disabled">
        <a-select-option v-for="(item, index) in codeList" :key="'selectList' + index" :value="item.id">{{ item.code
          }}</a-select-option>
      </a-select>
    </div>
    <self-detail-modal title="推广代号" :show-confirm="false" placement="center" v-model="propvisible"
      :confirmLoading="confirmLoading" :drawerWidth="'40%'" padding="0 24px" :close="false" @ok="handleOk"
      confirmText="确定">
      <template slot="centent" slot-scope="data">
        <div class="tagBox" :style="{
          height: data.height - 80 + 'px',
        }">
          <a-input-search placeholder="请输入要查找的推广代号" enter-button="查询" @search="searchQuery" @pressEnter="searchQuery"
            v-model="keyword" style="margin: 10px 0" />
          <div class="tagList">
            <a-tag class="slefTag" :color="selectCode.id == tag.id ? color : ''" @click="selectTag(tag)"
              v-for="(tag, tagindex) in codeList" :key="tagindex">{{ tag.code }}</a-tag>
            <a-list :data-source="codeList" />
          </div>
        </div>
      </template>
    </self-detail-modal>
  </div>
</template>

<script>
import { bindCode } from "@/api/api";
export default {
  name: "bisForSelect",
  model: {
    props: "value",
    event: "change",
  },
  props: {
    value: {
      type: Object,
    },
    isrequest: {
      type: Boolean,
      default: true,
    },
    hiddenTab: {
      type: Boolean,
      default: false,
    },
    showSelect: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    system: {
      type: String,
    },
  },
  watch: {
    value: function (newObj, oldObj) {
      if (!newObj) {
        this.$nextTick(() => {
          this.$emit("change", void 0);
          this.selectCode = {};
        });
        return;
      }
      this.selectCode = newObj;
    },
    propvisible: function (newVal) {
      if (!newVal) {
        this.codeList = [];
        this.keyword = "";
      }
    },
    keyword: function () {
      this.$debounce(
        () => {
          this.getCategory(this.keyword);
        },
        {
          timeKey: this.$options.name,
        }
      );
    },
  },
  data() {
    return {
      propvisible: false,
      confirmLoading: false,
      defaultActiveKey: "",
      codeList: [],
      tag_list: [],
      WxTabList: [],
      color: "#9373ee",
      keyword: "",
      selectCode: {},
    };
  },
  methods: {
    searchQuery() {
      this.getCategory(this.keyword);
    },
    // 获取推广代号
    getCategory(keyword) {
      this.confirmLoading = true;
      return new Promise((resolve, reject) => {
        bindCode
          .matchCode({
            keyword,
            is_bound: 1,
          })
          .then((res) => {
            if (res.code == 200) {
              this.codeList = res.data.list;
              resolve(res);
            } else {
              this.codeList = [];
              reject({ data: [] });
            }
            this.confirmLoading = false;
          });
      });
    },
    selectTag(item) {
      this.emit(item);
    },
    reset() {
      this.selectCode = {};
    },
    emit(e) {
      this.$emit("change", e);
    },
    handleOk() {
      this.propvisible = false;
    },
    openTag() {
      // if (!this.system) return this.$message.warning("请先选择门店");
      this.getCategory().then(() => {
        // 在获取代号列表后，如果当前有选中的代号，需要重新匹配正确的id
        if (this.value && this.value.code && this.codeList.length > 0) {
          const matchedCode = this.codeList.find(item => item.code === this.value.code);
          if (matchedCode && matchedCode.id !== this.value.id) {
            // 更新selectCode为正确的对象（包含正确的id）
            this.selectCode = matchedCode;
            // 同步更新表单值
            this.$emit("change", matchedCode);
          }
        }
      });
      this.propvisible = true;
    },
  },
};
</script>

<style lang="less" scoped>
.slefTag {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 5px 10px 5px 0;
  cursor: pointer;
}

.tagBox {
  display: flex;
  flex-flow: column;

  .tagList {
    flex: 1;
    height: 0;
    overflow-y: auto;
  }
}

/deep/ .ant-select-selection--multiple {
  height: 32px;
  padding-bottom: 3px;
  cursor: text;
  overflow: auto;
  zoom: 1;
}

/deep/ .ant-list-item-meta-title {
  margin-bottom: 0px;
  cursor: pointer;
}

.hiddenTab {
  /deep/ .ant-tabs-top-bar {
    display: none;
  }

  /deep/ .ant-tabs-content {
    margin-top: 10px;
  }
}
</style>