import {
    deleteAction,
    getAction,
    httpAction,
    postAction,
    putAction
} from "@/api/manage";
import Vue from "vue";
import { UI_CACHE_DB_DICT_DATA } from "@/store/mutation-types";
import $fileUpload from "@/utils/fileUpload";

import axios from "axios";
const mockApi = {
    // 用于本地数据测试
    test: params => {
        return new Promise(resolve => {
            axios.post(`/parameter/query`, params).then(res => {
                resolve({
                    code: 200,
                    data: res.data.data
                });
            });
        });
    }
};

export * from './modules/customerServiceApi'

//角色管理
const roleList = {
    index: params => getAction("/member/auth-role/index", params),
    indexForUser: params => getAction("/member/auth-role/selectable-roles", params),
    view: params => getAction("/member/auth-role/view", params),
    roleStaff: params => getAction("/member/member/role-staff", params),
    checkDept: params => getAction("/member/auth-role/check-dept", params), //选择范围
    setScope: params => postAction("/member/auth-role/set-scope", params), //设置范围
    deleteRoleUser: params => postAction("/member/auth-role/del-role-user", params),
    update: params => postAction("/member/auth-role/update", params),
    create: params => postAction("/member/auth-role/create", params),
    delete: params => deleteAction("/member/auth-role/delete", params),
    simulationLogin: params => postAction("/site/simulation-login", params)
};

const department = {
    index: params => getAction("/department/index", params),
    create: params => postAction("/department/create", params),
    update: params => postAction("/department/update", params),
    delete: params => deleteAction("/department/delete", params),
    getSubDept: params => getAction("/department/get-sub-dept", params),
    deptStaff: params => getAction("/member/member/dept-staff", params),
    selectForept: params => getAction("/member/member/select-for-dept", params),
    breadcrumb: params => getAction("/department/get-dept-chain-info", params),
    setStatus: params => postAction("/member/member/set-status", params),
    view: params => getAction("/department/view", params),
    memberView: params => getAction("/member/member/view", params),
    memberUpdate: params => postAction("/member/member/update", params)
};

const PublicApi = {
    relaySelect: params => getAction("/product-category/relay-select", params),
    relayChooseProduct: params => getAction("/goods/product/relay-choose", params),
    relayChoosePackage: params => getAction("/goods/package/relay-choose", params),
    customerRemainingProjects: params => getAction("/customer/customer-remaining-projects", params) // 客服订单详情
};

// 数据看板
const dashboard = {
    // 核心数据
    coreData: params => getAction("/dashboard/core-data", params),
    // 消耗分析
    adsCostData: params => getAction("/dashboard/ads-cost-data", params),
    // 加粉分析
    addFansData: params => getAction("/dashboard/add-fans-data", params),
    // 订金分析
    depositData: params => getAction("/dashboard/deposit-analysis", params),
    // 客服订金明细
    servicerDepositOrder: params => getAction("/dashboard/servicer-deposit-order", params),
    // 客服订金排行
    depositRank: params => getAction("/dashboard/deposit-ranked", params),
    //客服品牌分析
    branchAnalysis: params => getAction("/dashboard/branch-analysis", params)
};

// 定向
const direction = {
    index: params => getAction("/promote/direction/index", params),
    update: params => postAction("/promote/direction/update", params),
    create: params => postAction("/promote/direction/create", params),
    setStatus: params => postAction("/promote/direction/set-status", params),
    select: params => getAction("/promote/direction/select", params),
    promoteDept: params => getAction("/department/promote-dept", params)
};

// 腾讯数据源管理
const dataSource = {
    index: params => getAction("/promote/data-source/index", params),
    update: params => postAction("/promote/data-source/update", params),
    create: params => postAction("/promote/data-source/create", params),
    setStatus: params => postAction("/promote/data-source/set-status", params),
    select: params => getAction("/promote/data-source/select", params)
};

// 目标
const target = {
    conditionTypes: params => getAction("/target/field/condition-types", params),
    exportTemplate: params => getAction("/target/record/import-template", params), // 导出模板
    importTemplate: params => postAction("/target/record/import", params), // 导入模板
    create: params => postAction("/target/field/create", params),
    update: params => postAction("/target/field/update", params),
    setStatus: params => postAction("/target/field/set-status", params),
    select: params => getAction("/target/field/select", params),
    field: params => getAction("/target/field/selectable-field-list", params),
    report: params => getAction("/target/record/report", params)
};

// 二维码背景
const qrcodebgi = {
    index: params => getAction("/qrcode-bgi/index", params),
    select: params => getAction("/qrcode-bgi/select", params),
    update: params => postAction("/qrcode-bgi/update", params),
    create: params => postAction("/qrcode-bgi/create", params),
    setStatus: params => postAction("/qrcode-bgi/set-status", params)
};

// 银联
const unionPay = {
    index: params => getAction("/union-pay/index", params),
    view: params => getAction("/union-pay/view", params),
    select: params => getAction("/union-pay/select", params),
    update: params => postAction("/union-pay/update", params),
    create: params => postAction("/union-pay/create", params),
    setStatus: params => postAction("/union-pay/set-status", params),
    billNoTitleSelect: params => getAction("/union-pay/bill-no-title-select", params)
};

// 退订列表
const refund = {
    index: params => getAction("/refund/refund/index", params), //退订列表
    view: params => getAction("/refund/refund/view", params), //退订详情
    statusList: params => getAction("/refund/refund/status-list", params), //退款状态列表
    export: params => getAction("/refund/refund/export", params), //退订导出
    cancel: params => postAction("/refund/refund/cancel", params), //取消退订
    create: params => postAction("/refund/refund/create", params), //退订创建
    fail: params => postAction("/refund/refund/fail", params) //退订创建
};

// 退款理由
const refundReason = {
    index: params => getAction("/refund/reason/index", params), //退款列表
    select: params => getAction("/refund/reason/select", params), //获取下拉数据
    setStatus: params => postAction("/refund/reason/set-status", params), //修改状态
    create: params => postAction("/refund/reason/create", params), //创建
    update: params => postAction("/refund/reason/update", params) //修改
};
//报损管理
const reportedLoss = {
    index: params => getAction("/reported-loss/index", params),
    view: params => getAction("/reported-loss/view", params),
    create: params => postAction("/reported-loss/create", params),
    departmentInfo: params => getAction("/reported-loss/department-info", params),
    inventoryList: params => getAction("/reported-loss/inventory-list", params),
    export: params => getAction("/reported-loss/export-list", params),
    detailList: params => getAction("/reported-loss/detail-list", params),
    exportDetail: params => getAction("/reported-loss/export-detail-list", params),
    changeDetailStatus: params => postAction("/reported-loss/change-detail-status", params) // 修改明细状态
};

//-信息管理

const lklConfig = {
    index: params => getAction("/lkl-config/index", params), //分类下拉框
    view: params => getAction("/lkl-config/view", params), //分类下拉框
    create: params => postAction("/lkl-config/create", params), //分类下拉框
    updateData: params => postAction("/lkl-config/update-data", params), //分类下拉框
    status: params => postAction("/lkl-config/status", params) //分类下拉框
};

//--区域分类
const territorialSort = {
    selectCategory: params => getAction("/area/select-category", params), //分类下拉框
    view: params => getAction("/area/view", params), //分类详情
    setStatus: params => postAction("/area/set-status", params), //修改状态
    updateData: params => postAction("/area/update-data", params), //修改分类
    create: params => postAction("/area/create", params) //添加分类
};
//--扣款记录
const penalty = {
    penaltyCategory: params => getAction("/penalty/category/index", params), //违规类型列表
    create: params => postAction("/penalty/category/create", params), //添加违规类型
    setStatus: params => postAction("/penalty/category/set-status", params), //修改状态
    update: params => postAction("/penalty/category/update", params), //修改违规类型
    index: params => getAction("/penalty/record/index", params), //扣款列表
    view: params => getAction("/penalty/record/view", params), //扣款详情
    getRespnsiblePerson: params => getAction("/member/member/get-responsible-person", params), //责任人下拉
    getCateAll: params => getAction("/penalty/category/select", params), //扣款-违规类型
    recordCreate: params => postAction("/penalty/record/create", params), //添加扣款
    recordUpdate: params => postAction("/penalty/record/update", params), //修改扣款
    recordSetStatus: params => postAction("/penalty/record/set-status", params), //作废扣款
    export: params => getAction("/penalty/record/export", params) //导出
};
//--企微配置
const wxcomDeploy = {
    index: params => getAction("/wxcom/com/index", params), //企微配置列表
    view: params => getAction("/wxcom/com/view", params), //企微配置详情
    create: params => postAction("/wxcom/com/create", params), //添加企微配置
    update: params => postAction("/wxcom/com/update", params), //修改企微配置
    updateStatus: params => postAction("/wxcom/com/update-status", params), //修改企微状态
    delete: params => deleteAction("/wxcom/com/delete", params) //删除
};
//--企微服务商
const provider = {
    create: params => postAction("/wxcom/provider/create", params), // 创建
    update: params => postAction("/wxcom/provider/update", params), // 修改
    updateStatus: params => postAction("/wxcom/provider/update-status", params), // 修改状态
    view: params => getAction("/wxcom/provider/view", params), // 详情
    select: params => getAction("/wxcom/provider/select", params) // 下拉
};
//--企微服务商应用
const suite = {
    create: params => postAction("/wxcom/suite/create", params), // 创建
    update: params => postAction("/wxcom/suite/update", params), // 修改
    updateStatus: params => postAction("/wxcom/suite/update-status", params), // 修改状态
    view: params => getAction("/wxcom/suite/view", params), // 详情
    select: params => getAction("/wxcom/suite/select", params) // 下拉
};
//--企微服务商授权公司
const authCom = {
    update: params => postAction("/wxcom/auth-com/update", params) // 修改
};

//-- 企微直加
const wxcomAdd = {
    index: params => getAction("/wxcom/cus-button/index", params), // 列表
    view: params => getAction("/wxcom/cus-button/view", params), // 详情
    create: params => postAction("/wxcom/cus-button/create", params), // 创建
    update: params => postAction("/wxcom/cus-button/update", params), // 修改
    updateGroupBatch: params => postAction("/wxcom/cus-button/update-group-batch", params), // 批量修改分组
    updateStatus: params => postAction("/wxcom/cus-button/status", params), // 修改状态
    // 成员加粉统计
    todayCount: params => getAction("/wxcom/cus-button-count/today-count", params), // 获取统计数据
    indexForDate: params => getAction("/wxcom/cus-button-count/index-for-date", params), // 时间统计
    exportForDate: params => getAction("/wxcom/cus-button-count/export-for-date", params), // 时间统计导出
    indexForUser: params => getAction("/wxcom/cus-button-count/index-for-user", params), // 员工统计
    exportForUser: params => getAction("/wxcom/cus-button-count/export-for-user", params) // 员工统计导出
};

//--员工花名册
const staffRoster = {
    index: params => getAction("/feishu/user/index", params), //员工花名册列表
    view: params => getAction("/feishu/user/view", params), //员工花名册详情
    dataEntry: params => postAction("/feishu/user/data-entry", params), //资料录入
    abnormal: params => getAction("/feishu/user/abnormal", params), //异常记录
    export: params => getAction("/feishu/user/export", params), //导出
    boundMember: params => postAction("/feishu/user/bound-member", params), //绑定成员
    createMember: params => postAction("/feishu/user/create-member", params), //新增成员
};
//--订单类型管理
const orderType = {
    index: params => getAction("/order-type/index", params), //订单类型管理
    view: params => getAction("/order-type/view", params), //订单类型管理详情
    create: params => postAction("/order-type/create", params), //订单类型管理创建
    update: params => postAction("/order-type/update", params), //订单类型管理编辑
    setStatus: params => postAction("/order-type/set-status", params), //订单类型管理修改状态
    select: params => getAction("/order-type/select", params) //订单类型管理下拉
};

//--老师列表
const teacherType = {
    index: params => getAction("/teacher-job/index", params), //老师类型管理列表
    view: params => getAction("/teacher-job/view", params), //老师类型管理详情
    update: params => postAction("/teacher-job/update", params), //老师类型管理修改
    teacherSelect: params => getAction("/teacher-job/teacher-type", params), //老师类型管理下拉
    export: params => getAction("/teacher-job/export", params), //老师类型管理导出
    select: params => getAction("/teacher-level/select", params), //老师等级下拉
    levelIndex: params => getAction("/teacher-level/index", params), //老师等级列表
    levelCreate: params => postAction("/teacher-level/create", params), //老师等级创建
    levelUpdate: params => postAction("/teacher-level/update", params), //老师等级编辑
    setStatus: params => postAction("/teacher-level/set-status", params), //老师等级状态
    virtualList: params => getAction("/teacher-job/virtual-teacher-list", params), //虚拟老师列表
    virtualCreate: params => postAction("/teacher-job/virtual-teacher-create", params), //虚拟老师创建
    virtualDelete: params => postAction("/teacher-job/virtual-teacher-delete", params), //虚拟老师删除
    updateSchedule: params => postAction("/teacher-job/update-schedule", params), //虚拟老师状态
    scheduleList: params => getAction("/teacher-schedule-lock/schedule-list", params), //排班管理列表
    editSchedule: params => postAction("/teacher-schedule-lock/update-schedule", params) //档期老师-编辑排班档期
};

//--老师类型管理
const teacherTypeModal = {
    create: params => postAction("/teacher-type/create", params), //老师类型-新增
    update: params => postAction("/teacher-type/update", params), //老师类型-新增
    setStatus: params => postAction("/teacher-type/set-status", params), //老师类型-设置状态
    view: params => getAction("/teacher-type/view", params), //老师类型-详情
    select: params => getAction("/teacher-type/select", params), //老师类型-下拉
};

//--获取appid
const getAppid = {
    index: params => getAction("/config/index", params), //获取appid
    info: params => getAction("/wechat/user/info", params) //获取授权信息
};

//--门店管理
const storeManage = {
    storeExport: params => getAction("/store/export", params),
    storeView: params => getAction("/store/view", params),
    storeIndex: params => getAction("/store/index", params),
    storeCreate: params => postAction("/store/create", params),
    storeUpdate: params => postAction("/store/update", params),
    storeSetStatus: params => postAction("/store/set-status", params),
    storeUserList: params => getAction("/store/store-user", params),
    storeUserSearch: params => getAction("/store/search-user", params),
    storeUserAdd: params => postAction("/store/add-store-user", params),
    storeUserDel: params => deleteAction("/store/del-store-user", params),
    getProvinces: params => getAction("/common/provinces/index", params),
    authorizationList: params => getAction("/common/auth-interface/list", params),
    storeChoose: params => getAction("/store/choose", params), // 门店列表
    planStoreList: params => getAction("/store/plan-store-list", params), // 门店列表
    updateAreaBatch: params => postAction("/store/update-area-batch", params), // 批量修改门店区域
    updateAddress: params => postAction("/store/update-address", params), // 编辑地址
    teacherPlanTimeList: params => getAction("/store/teacher-plan-time-list", params), // 门店老师预约时间列表（档期）
    unionConfigView: params => getAction("/store/union-config-view", params), // 门店收款配置-详情
    getQrcode: params => getAction("store/get-qrcode", params), // 门店收款配置-详情
    payConfig: params => postAction("/store/union-config-update", params), // 门店收款配置-配置
    reachStoreExport: params => getAction("/order/order-header/reach-store-export", params),
    revertSettlement: params => postAction("/order/order-header/revert-settlement", params),
    changePlanStore: params => postAction("/order/order-header/change-plan-store", params),
    cancel: params => postAction("/order/order-header/cancel", params),
    storeCancel: params => postAction("/order/order-header/store-cancel", params),
    storeRetract: params => postAction("/order/order-header/store-retract", params),
    planTeacherList: params => getAction("/store/teacher-plan-list", params),
};

// 品牌
const brand = {
    create: params => postAction("/brand/create", params), // 创建品牌
    update: params => postAction("/brand/update", params), // 编辑品牌
    setStatus: params => postAction("/brand/set-status", params), // 修改品牌状态
    index: params => getAction("/brand/index", params), // 品牌列表
    select: params => getAction("/brand/select", params), // 品牌下拉列表
}


// 客户管理
const customer = {
    export: params => getAction("/customer/export", params),
    view: params => getAction("/customer/view", params),
    index: params => getAction("/customer/index", params),
    customerOrder: params => getAction("/customer/customer-order", params),
    updateMobileName: params => postAction("/customer/update-mobile-name", params),
    mobileCheck: params => getAction("/customer/mobile-check", params), //获取手机号
    void: params => postAction("/customer/void", params), //客户-作废
    serviceLogList: params => getAction("/wxcom/customer-service-log/index", params),
};

// 客资反馈
const customerFeedback = {
    index: params => getAction("/customer/feedback/index", params),
    export: params => getAction("/customer/feedback/export", params),
};

//  客服-分配
const customerService = {
    updateWxcomUserCustomerService: params => postAction("/customer-service/update-wxcom-user-customer-service", params),
    export: params => getAction("/customer-service/export", params),
    select: params => getAction("/customer-service/select", params),
    thirdSystemUsers: params => getAction("/customer-service/third-system-users", params),
    chooseWxcom: params => getAction("/wxcom/com/choose", params) //企微账号分配
};

//  客服-加粉
const customerCount = {
    indexDetail: params => getAction("/customer-service-count/index-detail", params),
    export: params => getAction("/customer-service-count/export", params)
};

//  客服-客户继承
const cusExtend = {
    create: params => postAction("/wxcom/cus-extend/create", params),
    statusList: params => getAction("/wxcom/cus-extend-detail/status-list", params),
    export: params => getAction("/wxcom/cus-extend-detail/export", params)
};

//预收金流水
const payRecord = {
    view: params => getAction("/pay-record/view", params), //流水详情
    updateServiceCustomer: params => postAction("/pay-record/update-servicer-customer", params), // 修改归属
    select: params => getAction("/member/member/select", params), //归属客服下拉框
    serviceSelect: params => getAction("/member/member/servicer-select", params), //创建客服下拉框
    wxcomSelect: params => getAction("/wxcom/user/select", params), //归属客服企微下拉框
    cusSelect: params => getAction("/wxcom/cus-customer/select", params), //归属客户下拉框
    releaseWater: params => postAction("/pay-record/release-water", params), // 释放流水
    freeze: params => postAction("/pay-record/freeze", params), // 冻结流水
    restore: params => postAction("/pay-record/restore", params), // 恢复流水
    transferWechat: params => postAction("/pay-record/wxcom-transfer-wechat", params) // 企微转个微
};

//  客服-订金客服排名
const subscriptionService = {
    index: params => getAction("/data/servicer-deposit-order/index", params),
    orderGroup: params => getAction("/data/servicer-deposit-order/index-for-group", params),
    export: params => getAction("/data/servicer-deposit-order/export", params),
    exportForGroup: params => getAction("/data/servicer-deposit-order/export-for-group", params)
};

//  客服-订金项目排名
const subscriptionProject = {
    index: params => getAction("/data/servicer-deposit-project-order/index", params),
    export: params => getAction("/data/servicer-deposit-project-order/export", params),
    orderGroup: params => getAction("/data/servicer-deposit-project-order/index-for-group", params),
    exportForGroup: params => getAction("/data/servicer-deposit-project-order/export-for-group", params),
    servicerRoles: params => getAction("/member/auth-role/servicer-roles", params)
};

// 客服-数据统计
const servicerAnalysis = {
    index: params => getAction("/data/servicer-analysis/index", params),  //个人排名
    export: params => getAction("/data/servicer-analysis/export", params),  //个人排名导出
    indexForGroup: params => getAction("/data/servicer-analysis/index-for-group", params),  //分组排名
    exportForGroup: params => getAction("/data/servicer-analysis/export-for-group", params),  //分组排名导出
}


// 门店业绩-数据统计
const storePerformanceAnalysis = {
    export: params => getAction("/data/store-performance-analysis/export", params),
    dailyExport: params => getAction("/data/store-performance-analysis/daily-analysis-export", params),
}

// 门店绑定老带新-
const customerIntroduced = {
    storeBind: params => postAction("/customer-introduced/store-bind", params),
}

//客服-到店统计
const storeStatistics = {
    index: params => getAction("/store/order-data", params),
    export: params => getAction("/store/order-data-export", params)
};

// 客服工作台-手机号查看日志列表
const mobileViewLog = {
    index: params => getAction("/customer/mobile-view-log/index", params)
};

const inventoryStatistics = {
    index: params => getAction("/store/inventory-data", params),
    export: params => getAction("/store/inventory-data-export", params)
};
//  客服-代号绑定
const bindCode = {
    select: params => getAction("/promote/bind-code/select", params),
    export: params => getAction("/promote/promoter/export", params),
    matchCode: params => getAction("/promote/code/choose", params),
    create: params => postAction("/promote/code/create", params),
    update: params => postAction("/promote/promoter/bind-code", params)
};
// 订单管理 - 订单
const theOrderManagementByorder = {
    getAllList: params => getAction("/promote/channel/get-all-list", params), // 渠道列表
    getListByOrder: params => getAction("/customer/get-list-by-order", params), // 用户列表
    getInfoByOrder: params => getAction("/customer/get-info-by-order", params), // 获取订单新增的用户详情
    export: params => getAction("/order/order-header/export", params), // 导出
    create: params => postAction("/order/order-header/create", params), // 创建
    update: params => postAction("/order/order-header/servicer-update-for-plan", params), // 修改
    view: params => getAction("/order/order-header/view", params), // 详情
    writeList: params => getAction("/order/order-header/write-list", params), // 流水
    cancellation: params => postAction("/order/order-header/cancellation", params), // 作废
    select: params => getAction("/order/order-header/select", params),
    refreshWriteList: params => postAction("/order/order-header/refresh-write-list", params), // 刷新流水
    orderPrepay: params => postAction("/order/order-header/order-prepay", params), // 核销
    depositExport: params => getAction("/order/deposit-details/export", params),
    mobileCheck: params => getAction("/order/order-header/mobile-check", params), //获取手机号
    servicerViewForPlan: params => getAction("/order/order-header/servicer-view-for-plan", params), // 客服订单详情
    customerInfo: params => getAction("/customer/customer-info", params), // bis-获取客户信息
    cusServicePerformanceList: params => getAction("/order/order-header/cus-service-performance", params), // 客服业绩订单列表
};

//商品分类
const goodsCategory = {
    goodsCategoryIndex: params => getAction("/product-category/index", params),
    goodsCategorySelect: params => getAction("/product-category/select", params),
    goodsCategoryCreate: params => postAction("/product-category/create", params),
    goodsCategoryUpdate: params => postAction("/product-category/update", params),
    goodsCategoryView: params => getAction("/product-category/view", params),
    goodsCategoryParentCate: params => getAction("/product-category/cate-parent", params), //获取父级分类
    goodsCategorySetStatus: params => postAction("/product-category/set-status", params)
};

//商品管理
const productManage = {
    productIndex: params => getAction("/product/index", params),
    productView: params => getAction("/product/view", params),
    productCreate: params => postAction("/product/create", params),
    productUpdate: params => postAction("/product/update", params),
    productSetStatus: params => postAction("/product/set-status", params)
};

//渠道
const promoteManage = {
    index: params => getAction("/promote/channel/index", params),
    view: params => getAction("/promote/channel/view", params),
    create: params => postAction("/promote/channel/create", params),
    update: params => postAction("/promote/channel/update", params),
    setStatus: params => postAction("/promote/channel/set-status", params),
    cityTime: params => getAction("/promote/analysis/start-stop-time", params), //城市消耗起止时间
    hourTime: params => getAction("/promote/analysis/hour-time", params), //时段分析起止时间
    cityData: params => getAction("/promote/analysis/city-data", params), //城市消耗数据
    cityDataExport: params => getAction("/promote/analysis/city-data-export", params), //城市消耗汇总数据导出
    hourData: params => getAction("/promote/analysis/hour-data", params), //时段分析数据
    hourDataExport: params => getAction("/promote/analysis/hour-data-export", params), //时段分析数据导出
    cityDataExportDetail: params => getAction("/promote/analysis/city-daily-data", params),
    cityDataExportPlus: params => getAction("/data/city-analysis/export", params), //城市消耗汇总数据导出
    cityDataExportDetailPlus: params => getAction("/data/store-analysis/export", params),
    getPullList: params => getAction("/promote/account/get-pull-list", params),
    cityAnalysisIndex: params => getAction("/data/city-analysis/index", params),
    storeAnalysisIndex: params => getAction("/data/store-analysis/index", params),
    userGrowthOutputExport: params => getAction("/promote/analysis/user-growth-output-export", params),//加粉产出分析导出
};

//推广子账户
const promoteManageAccount = {
    index: params => getAction("/promote/account/index", params),
    view: params => getAction("/promote/account/view", params),
    create: params => postAction("/promote/account/create", params),
    update: params => postAction("/promote/account/update", params),
    setStatus: params => postAction("/promote/account/set-status", params),
    export: params => getAction("/promote/account/export", params),
    filterList: params => getAction("/promote/agent/filter-list", params),
    setAgent: params => postAction("/promote/account/set-agent", params), // 修改代理商
    setRebates: params => postAction("/promote/account/set-rebates", params), // 修改返点
    updateDirectionBatch: params => postAction("/promote/account/update-direction-batch", params), // 修改返点
    getList: params => getAction("/promote/account/get-list", params), // 账号-选择搜索
    getPlanView: params => getAction("/promote/account/get-plan-view", params), // 子账户计划列表
    updateProgram: params => postAction("/promote/account-pull-record/update-program", params), // 更新子账户计划列表
    reportEventTagetSelect: params => getAction("/promote/account/report-event-taget-select", params), //推广上报事件目标
};

const promoteManageLocalAccount = {
    index: params => getAction("/promote/ads-local-account-sub/index", params),
    export: params => getAction("/promote/ads-local-account-sub/export", params),
    view: params => getAction("/promote/ads-local-account-sub/view", params),
    create: params => postAction("/promote/ads-local-account-sub/create", params),
    update: params => postAction("/promote/ads-local-account-sub/update", params),
    setStatus: params => postAction("/promote/ads-local-account-sub/set-status", params),
    setAgent: params => postAction("/promote/ads-local-account-sub/set-agent", params), // 修改代理商
    setRebates: params => postAction("/promote/ads-local-account-sub/set-rebates", params), // 修改返点
    updateDirectionBatch: params => postAction("/promote/ads-local-account-sub/update-direction-batch", params), // 修改返点
}

// 本地推线索列表
const adsLocalClueList = {
    index: params => getAction("/promote/ads-local-clue-list/index", params),
    export: params => getAction("/promote/ads-local-clue-list/export", params),
    mobileCheck: params => getAction("/promote/ads-local-clue-list/mobile-check", params),
    select: params => getAction("/promote/ads-local-clue-list/select", params),
    setStatus: params => postAction("/promote/ads-local-clue-list/set-status", params),
}

// 充值请款单
const requestpayout = {
    view: params => getAction("/promote/request-payout/view", params),
    create: params => postAction("/promote/request-payout/create", params),
    export: params => getAction("/promote/request-payout/export", params),
    update: params => postAction("/promote/request-payout/set-status", params),
    AdvanceDetailsExport: params => getAction("/promote/request-payout/advances-detail-export", params),
    topUpExport: params => getAction("/promote/request-payout/top-up-export", params)
};

//链路管理
const promoteLink = {
    index: params => getAction("/promote/promote-link/index", params),
    select: params => getAction("/promote/promote-link/select", params),
    create: params => postAction("/promote/promote-link/create", params),
    update: params => postAction("/promote/promote-link/update", params),
    setStatus: params => postAction("/promote/promote-link/set-status", params)
};

//项目管理
const promoteProject = {
    index: params => getAction("/promote/promote-project/index", params),
    select: params => getAction("/promote/promote-project/select", params),
    selectTree: params => getAction("/promote/project-group/select-tree", params),
    create: params => postAction("/promote/promote-project/create", params),
    update: params => postAction("/promote/promote-project/update", params),
    setStatus: params => postAction("/promote/promote-project/set-status", params)
};


//项目分组
const projectGrounp = {
    index: '/promote/project-group/index',
    setStatus: params => postAction("/promote/project-group/set-status", params),
    update: params => postAction("/promote/project-group/update", params),
    create: params => postAction("/promote/project-group/create", params),
    select: params => getAction("/promote/project-group/select", params),
    view: params => getAction("/promote/project-group/view", params),
};

//主体管理
const promoteMainBody = {
    index: params => getAction("/promote/ads-main-body/index", params),
    create: params => postAction("/promote/ads-main-body/create", params),
    update: params => postAction("/promote/ads-main-body/update", params),
    setStatus: params => postAction("/promote/ads-main-body/set-status", params),
    select: params => getAction("/promote/ads-main-body/select", params),
};

//物料管理
const materialManage = {
    index: params => getAction("/material/goods/index", params),
    export: params => getAction("/material/goods/export", params),
    view: params => getAction("/material/goods/view", params),
    update: params => postAction("/material/goods/update", params),
    create: params => postAction("/material/goods/create", params),
    batchCreate: params => postAction("/material/goods/batch-create", params),
    setStatus: params => postAction("/material/goods/set-status", params),
    searchByBarcode: params => postAction("/material/goods/get-info-all", params),
    getUpdateHistor: params => getAction("/material/goods/get-update-history", params),
    select: params => getAction("/material/goods/select", params),
    choose: params => getAction("/material/goods/choose", params),
    getBelongWarehouse: params => getAction("/material/inventory/get-belong-warehouse", params)
};

//供应商
const supplierManage = {
    index: params => getAction("/material/supplier/index", params),
    view: params => getAction("/material/supplier/view", params),
    update: params => postAction("/material/supplier/update", params),
    create: params => postAction("/material/supplier/create", params),
    setStatus: params => postAction("/material/supplier/set-status", params),
    search: params => getAction("/material/supplier/search", params)
};

// 库存
const inventoryManage = {
    index: params => getAction("/material/inventory/index", params), //库存列表
    export: params => getAction("/material/inventory/export", params), //导出
    materialInventoryDetailExport: params => getAction("/material/inventory/material-inventory-detail-export", params),
    recordList: params => getAction("/material/inventory/record-list", params), //库存明细列表
    recordExport: params => getAction("/material/inventory/record-export", params), //库存明细导出
    inbound: params => postAction("/material/inventory/inbound", params), //入库
    outbound: params => postAction("/material/inventory/outbound", params), //出库
    check: params => postAction("/material/inventory/check", params), //盘点
    allot: params => postAction("/material/inventory/allot", params), //调拨
    searchStoreByEntity: params => getAction("/store/filter-list", params), //params：keyword 根据关键字模糊查询门店
    storeSelect: params => getAction("/store/select", params), //门店下拉
    getQrcode: params => getAction("/store/get-qrcode", params), //门店下拉
    approveCreate: params => postAction("/material/approve/create", params), //创建调拨单
    approveIndex: params => getAction("/material/approve/allot-index", params), //调拨单列表
    approveView: params => getAction("/material/approve/view", params),
    approveDetail: params => getAction("/material/approve/detail-list", params),
    approveDetailExport: params => getAction("/material/approve/export-detail-list", params),
    changeDetailStatus: params => postAction("/material/approve/change-detail-status", params), // 修改明细状态
    inventoryList: params => getAction("/reported-loss/inventory-list", params),
    exportInventoryList: params => getAction("/material/check/batch-inventory-export", params), //批量导出盘点明细
    //approveExamine:params => postAction('/material/approve/send',params), 审批调拨单
    approveSend: params => postAction("/material/approve/send", params), //审批调拨单发货
    approveReceive: params => postAction("/material/approve/receive", params), //审批调拨单收货
    approveCancel: params => postAction("/material/approve/cancel", params), //取消调拨单
    allotExport: params => getAction("/material/approve/allot-index-export", params), //审批列表导出
    inventory: params => postAction("/material/inventory/record-list", params), //盘点列表
    inventoryCheck: params => postAction("/material/check/create-inventory", params), //创建盘点单
    inventoryDetail: params => getAction("/material/check/inventory-view", params), //盘点单详情
    saveInventory: params => postAction("/material/check/create-inventory-details", params), //保存盘点单
    cancelInventory: params => getAction("/material/check/update-status", params), //取消盘点单
    reportedLoss: params => getAction("/reported-loss/index", params), //报损列表
    purchaseIndex: params => getAction("/procurement/index", params), //采购列表
    addGoods: params => getAction("/procurement/inventory-list", params), //采购申请获取商品
    creatPurchaseIndex: params => postAction("/procurement/create", params), //创建采购单
    purchaseDetail: params => getAction("/procurement/view", params), //采购单详情
    changePurchaseNum: params => postAction("/procurement/change-purchase-num", params),//修改采购数量
    shipmentGoods: params => postAction("/procurement/shipment", params), //发货
    acceptanceGoods: params => postAction("/procurement/acceptance", params), //收货
    canclePurchase: params => postAction("/procurement/cancle", params), //取消采购
    changePrice: params => postAction("/procurement/change-prices", params), //修改价格
    handleError: params => postAction("/procurement/handle-exception", params), //处理采购异常
    exportList: params => getAction("/procurement/export", params), //采购单批量导出
    getDetail: params => getAction("/material/goods/get-material-inventory-detail", params), //获取物料详情
    purchasePayDetail: params => getAction("/material/procurement-pay/view", params), //采购付款详情
    purchasePayCreate: params => postAction("/material/procurement-pay/create", params), //采购付款创建
    purchasePayFeiShuApprove: params => postAction("/material/procurement-pay/approve", params), //采购付款单飞书发起
    payTypeSelect: params => getAction("/material/procurement-pay/pay-type-select", params), //采购付款方式
    detailListColumns: params => getAction("/procurement/detail-list-columns", params), //采购跟进表列表字段
    detailView: params => getAction("/procurement/detail-view", params), //采购商品详情
    detailExport: params => getAction("/procurement/detail-export", params), //采购商品详情导出

    //  分类
    inventorySetStatus: params => postAction("/product-category/inventory-set-status", params),
    inventoryView: params => getAction("/product-category/inventory-view", params),
    inventoryCreate: params => postAction("/product-category/inventory-create", params),
    inventoryUpdate: params => postAction("/product-category/inventory-update", params),
    inventorySelect: params => getAction("/product-category/inventory-index", params)
};

//售后工作台
// 售后工作台-投诉建议
const complaint = {
    list: params => getAction("/complaint/list", params), //投诉列表
    info: params => getAction("/complaint/info", params), //获取详情
    mobile: params => getAction("/complaint/mobile", params), //获取手机号
    dealWith: params => postAction("/complaint/deal-with", params), //处理投诉
    sourceTypeAll: params => getAction("/complaint/source-type-all", params), //获取投诉来源
    reasonAll: params => getAction("/complaint/reason-all", params), //获取投诉原因
    export: params => getAction("/complaint/export", params), //导出
    reasonList: params => getAction("/complaint/reason-list", params), //获取投诉原因列表
    reasonStatusUpdate: params => postAction("/complaint/reason-status-update", params), //修改投诉原因状态
    reasonCreate: params => postAction("/complaint/reason-create", params), //投诉原因新增
    reasonUpdate: params => postAction("/complaint/reason-update", params) //修改投诉原因
};

//流量工作台
// 流量数据分析
const DataAnalysis = {
    // 可选时间段
    projectPublicTime: params => getAction("/promote/analysis/public-time", params),
    // 投放人分析
    castPeopleDetails: params => getAction("/promote/analysis/cast-people-details", params), // 投放人分析详情
    // castPeopleTime: params => getAction('/promote/analysis/cast-people-time', params), // 投放人分析时间
    castPeopleExport: params => getAction("/promote/analysis/cast-people-export", params), // 投放人分析明细导出
    castPeopleDetailsExport: params => getAction("/promote/analysis/cast-people-details-export", params), // 投放人分析明细导出
    // 项目分析
    projectExport: params => getAction("/promote/analysis/project-export", params), // 项目分析明细导出
    projectDetails: params => getAction("/promote/analysis/project-details", params), // 项目分析详情
    // 平台分析
    platformDetails: params => getAction("/promote/analysis/promote-details", params), // 平台分析详情
    platformExport: params => getAction("/promote/analysis/promote-export", params), // 平台分析明细导出
    // 对账单
    // platformExport: params => getAction('/promote/account-check/index', params),  // 平台分析明细导出
    //素材数据
    materialDataExport: params => getAction("/promote/analysis/material-analysis-export", params),
    //年龄性别数据
    ageGenderDataExport: params => getAction("/promote/analysis/age-gender-analysis-export", params),
};
//推广人员
const promoteAccount = {
    index: params => getAction("/promote-account/index", params),
    view: params => getAction("/promote-account/view", params),
    create: params => postAction("/promote-account/create", params),
    update: params => postAction("/promote-account/update", params),
    setStatus: params => postAction("/promote-account/set-status", params),
    setCostDiscount: params => postAction("/promote-account/set-cost_discount", params), //params: id , cost_discount(消耗值)
    getPromotePerson: params => getAction("/member/member/get-promote-person", params) //params: username(用户名)
};

//代理商
const promoteAgent = {
    index: params => getAction("/promote/agent/index", params),
    export: params => getAction("/promote/agent/export", params),
    view: params => getAction("/promote/agent/view", params),
    create: params => postAction("/promote/agent/create", params),
    update: params => postAction("/promote/agent/update", params),
    setStatus: params => postAction("/promote/agent/set-status", params),
    bankView: params => getAction("/promote/agent/bank-view", params),
    advancesList: params => getAction("/promote/request-payout/advances-list", params)
};

//客服微信号管理
const wechatAccount = {
    index: params => getAction("/weixin-account/index", params),
    view: params => getAction("/weixin-account/view", params),
    create: params => postAction("/weixin-account/create", params),
    update: params => postAction("/weixin-account/update", params),
    setStatus: params => postAction("/weixin-account/set-status", params),
    getService: params => getAction("/member/member/get-service", params) //params: username(用户名)
};

//微信客服链路配置管理
const wechatServicer = {
    create: params => postAction("/wxcom/wechat-servicer/create", params),
    select: params => getAction("/wxcom/wechat-servicer/select", params),
    view: params => getAction("/wxcom/wechat-servicer/view", params),
    replyCreate: params => postAction("/wxcom/wechat-servicer-reply/update", params),
    replyView: params => getAction("/wxcom/wechat-servicer-reply/view", params),
    updateServicer: params => postAction("/wxcom/wechat-servicer/update-servicer", params),
    setStatus: params => postAction("/wxcom/wechat-servicer/set-status", params),
    delete: params => deleteAction("/wxcom/wechat-servicer/delete", params),
    updateResponsible: params => postAction("/wxcom/wechat-servicer/update-responsible", params),
    collectList: params => postAction("/wxcom/wechat-servicer/collect-list", params),
    collectExprot: params => getAction("/wxcom/wechat-servicer/export", params),
    collectListExport: params => getAction("/wxcom/wechat-servicer/collect-list-export", params)
};

const runningWater = {
    qrCode: params => postAction("/qrcode-bgi/qr-code", params),
    qrCodeIndex: params => getAction("/qrcode-bgi/index", params),
    changeQrcode: params => postAction("/qrcode-bgi/change-qrcode", params)
};

const wechatAccount_wct = {
    index: params => getAction("/wechat/wechat-account/index", params),
    view: params => getAction("/wechat/wechat-account/view", params),
    select: params => getAction("/wechat/wechat-account/select", params),
    promoteSelect: params => getAction("/wechat/wechat-account/promote-select", params),
    update: params => postAction("/wechat/wechat-account/update", params),
    create: params => postAction("/wechat/wechat-account/create", params),
    setStatus: params => postAction("/wechat/wechat-account/set-status", params),
    export: params => getAction("/wechat/wechat-account/export", params)
};

const wechatAllot = {
    index: params => getAction("/wechat/wechat-allot/index", params),
    view: params => getAction("/wechat/wechat-allot/view", params),
    export: params => getAction("/wechat/wechat-allot/export", params),
    select: params => getAction("/wechat/wechat-allot/select", params),
    update: params => postAction("/wechat/wechat-allot/update", params),
    create: params => postAction("/wechat/wechat-allot/create", params),
    setStatus: params => postAction("/wechat/wechat-allot/set-status", params)
};

const fansRecord = {
    index: params => getAction("/wechat/fans-record/index", params),
    update: params => postAction("/wechat/fans-record/update", params),
    create: params => postAction("/wechat/fans-record/create", params),
    export: params => getAction("/wechat/fans-record/export", params),
    batchCreate: params => postAction("/wechat/fans-record/batch-create", params),
    select: params => getAction("/wechat/fans-record/select", params)
};

//数据分析
//加粉订人转化分析
const addFansAnalysis = {
    index: params => getAction("/data/trans-analysis-add-fans/index", params),
    export: params => getAction("/data/trans-analysis-add-fans/export", params)
};
//订金到店转化分析
const depStoreAnalysis = {
    index: params => getAction("/data/trans-analysis-add-fans/index", params),
    export: params => getAction("/data/trans-analysis-order-store/export", params)
};

//加粉到店转化分析
const fanStoreAnalysis = {
    index: params => getAction("/data/trans-analysis-add-fans-store/index", params),
    export: params => getAction("/data/trans-analysis-add-fans-store/export", params)
};

//城市加粉订人转化分析
const addFansCityAnalysis = {
    index: params => getAction("/data/trans-analysis-city-add-fans/index", params),
    export: params => getAction("/data/trans-analysis-city-add-fans/export", params)
};

//城市加粉到店转化分析
const fanStoreCityAnalysis = {
    index: params => getAction("/data/trans-analysis-city-add-fans-store/index", params),
    export: params => getAction("/data/trans-analysis-city-add-fans-store/export", params)
};


//新客维护数据分析
const teacherCusAnalysis = {
    index: params => getAction("/data/teacher-cus-analysis/index", params),
    export: params => getAction("/data/teacher-cus-analysis/export", params),
    dateIndex: params => getAction("/data/teacher-cus-analysis/date-index", params),
    dateExport: params => getAction("/data/teacher-cus-analysis/date-export", params),
};

//老师业绩分析
const teacherAnalysis = {
    index: params => getAction("/data/teacher-analysis/list", params),
    export: params => getAction("/data/teacher-analysis/list-export", params),
    detailExport: params => getAction("/data/teacher-analysis/detail-export", params),
};

//部门管理
const departmentManage = {
    index: params => getAction("/department/index", params),
    update: params => postAction("/department/update", params),
    create: params => postAction("/department/create", params)
};

//固定资产
const fixedAssetManage = {
    setStatus: params => postAction("/assets/fixed-assets/set-status", params), //修改状态
    searchByBarcode: params => postAction("/assets/fixed-assets/check-info-all", params),
    index: params => getAction("/assets/fixed-assets/index", params), //库存列表
    view: params => getAction("/assets/fixed-assets/view", params), //库存列表
    recordList: params => getAction("/assets/fixed-assets/record-list", params), //库存明细列表
    inbound: params => postAction("/assets/fixed-assets/inbound", params), //入库
    create: params => postAction("/assets/fixed-assets/create", params), //新增
    list: params => getAction("/assets/fixed-assets/fixed-index", params), //物料列表
    update: params => postAction("/assets/fixed-assets/update", params),
    batchCreate: params => postAction("/material/goods/batch-create", params),
    outbound: params => postAction("/assets/fixed-assets/department-outbound", params), //出库
    allot: params => postAction("/assets/fixed-assets/allot", params), //调拨
    recordExport: params => getAction("/assets/fixed-assets/record-export", params), //明细导出
    department: params => getAction("/assets/fixed-assets/dept-index", params), // 部门列表
    checkCreate: params => postAction("/assets/fixed-assets/check-create", params), //校验导入信息
    inventory: params => postAction("/assets/fixed-assets/inventory", params), //盘点信息校验
    export: params => getAction("/assets/fixed-assets/export", params), //导出

    //  分类
    fixedSetStatus: params => postAction("/product-category/fixed-set-status", params),
    fixedView: params => getAction("/product-category/fixed-view", params),
    fixedCreate: params => postAction("/product-category/fixed-create", params),
    fixedUpdate: params => postAction("/product-category/fixed-update", params),
    fixedSelect: params => getAction("/product-category/fixed-index", params)
};

// 消耗管理
const consumption = {
    index: "/promote/ads-account-data/index", //消耗-列表
    export: params => getAction("/promote/ads-account-data/export", params), //消耗-列表导出
    create: params => postAction("/promote/ads-account-data/create", params), //消耗-创建消耗
    view: params => getAction("/promote/ads-account-data/view", params), //消耗-消耗详情
    update: params => postAction("/promote/ads-account-data/update", params), //消耗-编辑消耗
    confirm: params => postAction("/promote/ads-account-data/confirm", params), //消耗-确认消耗
    // 消耗
    setAgent: params => postAction("/promote/ads-account-data/set-agent", params), // 修改代理商
    setRebates: params => postAction("/promote/ads-account-data/set-rebates", params), // 修改返点
    // 消耗拉取列表
    recordindex: "/promote/account-pull-record/index", //消耗-列表
    create: params => postAction("/promote/account-pull-record/create", params) //消耗-创建消耗
};

//素材管理
const adsMaterial = {
    list: params => getAction("/promote/ads-material/index", params), //素材列表
    detail: params => getAction("/promote/ads-material/detail", params), //素材详情
    export: params => getAction("/promote/ads-material/export", params), //素材-列表导出
    changeComposer: params => postAction("/promote/ads-material/change-composer", params), //素材-修改创作人
    changeRefurbisher: params => postAction("/promote/ads-material/change-refurbisher", params), //素材-修改翻新人
    updateNote: params => postAction("/promote/ads-material/update-note", params), //素材-修改备注
    bundleList: params => getAction("/promote/ads-material/bundle-list", params), //素材-捆绑列表
    bundle: params => postAction("/promote/ads-material/bundle", params), //素材-捆绑
    unbundling: params => postAction("/promote/ads-material/unbundling", params), //素材-解绑
};

// 消耗拉取
const accountpullrecord = {
    index: "/promote/account-pull-record/index",
    create: params => postAction("/promote/account-pull-record/create", params),
};

//  落地页
const landPageList = {
    index: "/promote/analysis/land-page-list", // 落地页数据统计列表
    view: "/promote/land-page/index", // 落地页列表
    landPageExport: params => postAction("/promote/analysis/land-page-export", params) // 落地页列表
};

//  落地页
const landPageh5 = {
    landPageH5Delete: params => postAction("/promote/land-page/delete", params), // 删除落地页
    reportList: "/promote/land-page/report-list", // 上报列表
    reportCreate: params => postAction("/promote/land-page/report-create", params), // 上报创建
    copy: params => postAction("/promote/land-page/copy", params), // 落地页-复制
    reportUpdate: params => postAction("/promote/land-page/report-update", params), // 上报修改
    reportDelete: params => postAction("/promote/land-page/report-delete", params), // 上报删除
    selectedList: params => getAction("/promote/land-page/selected-list", params), // 下拉相关
    reportEventList: params => getAction("/promote/land-page/report-event-list", params), // 上报事件
    reportAccountList: params => getAction("/promote/land-page/report-account-list", params), // 上报账户
    updateOwnerBatch: params => postAction("/promote/land-page/update-owner-batch", params), // 修改负责人
    landSubmit: params => postAction("/promote/land-page/land-submit", params), // 落地页提审
    reportPlatform: params => postAction("/promote/land-page/report-platform", params) // 上报数据到平台
};

//  落地页分组
const landPageGroup = {
    view: params => postAction("/land-page-group/view", params),
    index: params => getAction("/land-page-group/index", params), // 分组列表
    create: params => postAction("/land-page-group/create", params), // 创建分组
    select: params => getAction("/land-page-group/select", params), // 分组下拉
    update: params => postAction("/land-page-group/update", params), // 修改分组
    delete: params => deleteAction("/land-page-group/delete", params), // 删除分组
    updateGroupBatch: params => postAction("/promote/land-page/update-group-batch", params) // 修改落地页分组
};

//  小程序
const Applet = {
    list: "/applet/index", // 小程序列表
    view: params => getAction("/applet/view", params), // 小程序详情
    setStatus: params => postAction("/applet/set-status", params), // 小程序修改状态
    update: params => postAction("/applet/update", params), // 小程序编辑
    create: params => postAction("/applet/create", params), // 小程序新增
    export: params => getAction("/applet/export", params), // 小程序导出
    select: params => getAction("/applet/select", params), // 小程序下拉
    transferLandPage: params => postAction("/applet/transfer-land-page", params), // 小程序替换
    selectCreated: params => getAction("/member/member/select-created", params), // 创建人下拉
    release: params => postAction("/applet/release", params), // 小程序发布状态
    appletUpload: params => postAction("/applet/upload", params) // 上传小程序
};

//  对账单
const accountCheck = {
    index: "/promote/account-check/index", // 对账单-列表
    export: params => getAction("/promote/account-check/export", params) // 对账单-列表
};

//  活码模块-活码管理
const cusQrcode = {
    view: params => getAction("/wxcom/cus-qrcode/view", params),
    select: params => getAction("/wxcom/cus-qrcode/select", params),
    selectLink: params => getAction("/wxcom/cus-qrcode/select-link", params),
    export: params => getAction("/wxcom/cus-qrcode/export", params),
    create: params => postAction("/wxcom/cus-qrcode/create", params),
    update: params => postAction("/wxcom/cus-qrcode/update", params),
    updateGroupBatch: params => postAction("/wxcom/cus-qrcode/update-group-batch", params),
    adminCode: params => getAction("/other-system/admin-code", params),
    chooseCode: params => getAction("/promote/code/choose", params),
    updateStatus: params => postAction("/wxcom/cus-qrcode/update-status", params) //禁用启用
};

//  活码模块-动态码管理
const cusDynamicQrcode = {
    view: params => getAction("/wxcom/cus-dynamic-qrcode/view", params),
    select: params => getAction("/wxcom/cus-dynamic-qrcode/select", params),
    export: params => getAction("/wxcom/cus-dynamic-qrcode/export", params),
    create: params => postAction("/wxcom/cus-dynamic-qrcode/create", params),
    update: params => postAction("/wxcom/cus-dynamic-qrcode/update", params),
    updateGroupBatch: params => postAction("/wxcom/cus-dynamic-qrcode/update-group-batch", params),
    updateStatus: params => postAction("/wxcom/cus-dynamic-qrcode/set-status", params) //禁用启用
};

//  活码模块-活码分组
const cusQrgroup = {
    index: params => getAction("/wxcom/cus-qrgroup/index", params),
    update: params => postAction("/wxcom/cus-qrgroup/update", params),
    delete: params => deleteAction("/wxcom/cus-qrgroup/delete", params),
    create: params => postAction("/wxcom/cus-qrgroup/create", params),
    select: params => getAction("/wxcom/cus-qrgroup/select", params)
};

//  活码模块-数据统计
const cusCount = {
    todayCount: params => getAction("/wxcom/cus-qrcode-count/today-count", params),
    userCountExport: params => getAction("/wxcom/cus-qrcode-count/export-for-user", params),
    dateCountExport: params => getAction("/wxcom/cus-qrcode-count/export-for-date", params),
    exportForQrcode: params => getAction("/wxcom/cus-customer-user/export-for-qrcode", params)
};

//  活码模块-动态活码统计
const cusDynamicQrcodeCount = {
    todayCount: params => getAction("/wxcom/cus-dynamic-qrcode-count/today-count", params),
    userCountExport: params => getAction("/wxcom/cus-dynamic-qrcode-count/index-for-user", params),
    dateCountExport: params => getAction("/wxcom/cus-dynamic-qrcode-count/export", params),
    exportForUser: params => getAction("/wxcom/cus-dynamic-qrcode-count/export-for-user", params)
};

//  标签管理
const useTag = {
    select: params => getAction("/wxcom/cus-tag/select", params),
    create: params => postAction("/wxcom/cus-tag/create", params), //添加标签
    delete: params => deleteAction("/wxcom/cus-tag/delete", params) //删除标签
};

// 内部成员-聊天记录
const wxcomMessage = {
    FriendList: params => getAction("/wxcom/user/friend-list", params),//成员
    messageList: params => getAction("/wxcom/message/index", params),//消息列表
};

//  通讯录-外部成员
const wxcomUser = {
    select: params => getAction("/wxcom/user/select", params),
    view: params => getAction("/wxcom/cus-customer-user/view", params),
    export: params => getAction("/wxcom/cus-customer-user/export", params),
    getAddWaySelect: params => getAction("/wxcom/cus-customer-user/get-add-way-select", params),
    getAdPositionSelect: params => getAction("/wxcom/cus-customer-user/get-ad-position-select", params),
    setPromoteStatus: params => postAction("/wxcom/user/set-promote-status", params),
    setStatus: params => postAction("/wxcom/user/set-status", params),
};

//  通讯录-内部成员
const wxcomDepartment = {
    tree: params => getAction("/wxcom/department/tree", params)
};

// 渠道管理
const channel = {
    select: params => getAction("/promote/channel/select", params),
    choose: params => getAction("/promote/channel/choose", params),
    getTree: params => getAction("/promote/channel/get-tree", params),
    storeSelect: params => getAction("/promote/channel/store-select", params)
};
//  部门成员-活码分组
const cususer = {
    getServiceUsers: params => getAction("/wxcom/user/com-service-users", params),
    quitUserIndex: params => getAction("/wxcom/quit-user/index", params),
    getComWithServiceUsers: params => getAction("/wxcom/user/service-users", params),
    wechatAccount_wct_choose: params => getAction("/wechat/wechat-account/choose", params)
};

//  部门成员-活码分组
const com = {
    select: params => getAction("/wxcom/com/select", params)
};

// 商品-标签
const goodTag = {
    index: params => getAction("/goods/tag/index", params), // 列表
    view: params => getAction("/goods/tag/view", params), // 详情
    create: params => postAction("/goods/tag/create", params), // 创建
    update: params => postAction("/goods/tag/update", params), // 修改
    setStatus: params => postAction("/goods/tag/set-status", params), // 设置状态
    deleteTag: params => postAction("/goods/tag/delete-tag", params), // 删除
    select: params => getAction("/goods/tag/select", params) // 下拉
};

//赠品列表
const gift = {
    index: params => getAction("/gift/index", params), // 列表
    giftlist: params => getAction("/goods/stored-value-card/gift-list", params), // 列表
    export: params => getAction("/gift/export", params), // 导出
    goodList: params => getAction("/gift/goods-list", params), // 商品选择
    create: params => postAction("/gift/create", params), // 创建
    setStatus: params => postAction("/gift/set-status", params) // 设置状态
};

// 商品-服务
const goodProduct = {
    index: params => getAction("/goods/product/index", params), // 列表
    create: params => postAction("/goods/product/create", params), //  创建
    update: params => postAction("/goods/product/update", params), // 修改
    export: params => getAction("/goods/product/export", params), // 服务导出
    view: params => getAction("/goods/product/view", params), // 详情
    copyProduct: params => postAction("/goods/product/copy-product", params), // 复制商品
    setStatus: params => postAction("/goods/product/set-status", params), // 设置上下架
    choose: params => getAction("/goods/product/choose", params) // 设置上下架
};
// 商品-客装品
const productMaterial = {
    index: params => getAction("/goods/product-material/index", params), // 列表
    view: params => getAction("/goods/product-material/view", params), // 详情
    create: params => postAction("/goods/product-material/create", params), //  创建
    update: params => postAction("/goods/product-material/update", params), // 修改
    export: params => getAction("/goods/product-material/export", params), // 服务导出
    copyProduct: params => postAction("/goods/product-material/copy-product", params), // 复制商品
    setStatus: params => postAction("/goods/product-material/set-status", params) // 设置上下架
};
// 商品-套餐
const goodPackage = {
    index: params => getAction("/goods/package/index", params), // 列表
    view: params => getAction("/goods/package/view", params), // 详情
    create: params => postAction("/goods/package/create", params), // 创建
    update: params => postAction("/goods/package/update", params), // 修改
    delete: params => deleteAction("/goods/package/delete", params), // 删除
    export: params => getAction("/goods/package/export", params), // 导出
    copy: params => postAction("/goods/package/copy", params), // 复制
    setStatus: params => postAction("/goods/package/set-status", params), // 导出
    select: params => getAction("/goods/package/select", params), // 下拉
    choose: params => getAction("/goods/package/choose", params) // 列表
};
// 商品-储值卡
const storedValueCard = {
    view: params => getAction("/goods/stored-value-card/view", params), // 储值卡详情
    setStatus: params => postAction("/goods/stored-value-card/set-status", params), // 设置上下架
    goodsList: params => getAction("/goods/stored-value-card/goods-list", params), // 商品选择
    create: params => postAction("/goods/stored-value-card/create", params), // 创建储值卡
    update: params => postAction("/goods/stored-value-card/update", params), // 修改储值卡
    export: params => getAction("/goods/stored-value-card/export", params), // 导出useableList: params =>
    useableList: params => postAction("/customer_recharge/customer-recharge-card/useable-list", params) // 储值卡抵扣可用列表
};

// 门店工作台-预约到店
const orderStore = {
    switchStoreList: params => getAction("/store/switch-store-list", params), // 切换门店列表
    storeList: params => getAction("/order/order-header/store-list", params), // 预约到店列表
    storeCusList: params => getAction("/order/order-header/store-cus-list", params), // 门店客户可使用订单
    updateOrderStatus: params => postAction("/order/order-header/update-order-status", params), // 订单到店状态
    storeViewForEdit: params => getAction("/order/order-header/store-view-for-edit", params), // 门店订单详情-用于编辑
    storeSave: params => getAction("/order/order-header/store-save", params), // 门店订单详情-用于编辑
    storeSettlement: params => postAction("/order/order-header/store-settlement", params), // 门店订单详情-保存
    storeCreate: params => postAction("/order/order-header/store-create", params), // 门店订单详情-保存
    storeViewForPlan: params => getAction("/order/order-header/store-view-for-plan", params) // 门店订单详情-保存
};

// 门店工作台-新增预约
const storeGoods = {
    goodsUnionindex: params => getAction("/view/goods-union/index", params), // 可选商品列表（套餐单品）
    otherCreate: params => postAction("/order/order-header/other-create", params), // 可选商品列表（套餐单品）
    storeUpdateForPlan: params => postAction("/order/order-header/store-update-for-plan", params), // 可选商品列表（套餐单品）
};
//门店工作台-储值卡销售
const cardsSales = {
    cardList: params => getAction("/customer_recharge/customer-recharge-card/card-list", params), // 卡项列表
    storeSearchList: params => getAction("/customer/store-search-list", params), // 门店搜索客户列表
    rechargeCard: params => getAction("/customer_recharge/customer-recharge-card/index", params), // 已购卡项
    storeList: params => getAction("/teacher-job/store-list", params), // 门店老师列表
    setOrder: params => postAction("/customer_recharge/customer-recharge-card/set-order", params), // 储值卡立即收款下单
    storeCreate: params => postAction("/customer/store-create", params), // 门店新增用户
    grouPlatformIndex: params => getAction("/group-platform/index", params), // 团购平台
    view: params => getAction("/customer_recharge/customer-recharge-card/view", params), // 查看详情
    extendExpire: params => postAction("/customer_recharge/customer-recharge-card/extend-expire", params), // 延长有效期
    getAgeBracket: params => getAction("/customer/get-age-bracket", params) // 获取年龄段
};
//门店工作台-支付收款
const payCode = {
    view: params => getAction("/customer_recharge/customer-recharge-card/view", params), // 获取订单详情
    unionScanPay: params => postAction("/pay/payment/union-scan-pay", params), // 扫码枪支付
    getDataByOrderId: params => getAction("/order/pay/get-data-by-order-id", params), // 已收金额明细
    storePayRecordList: params => getAction("/customer_recharge/customer-recharge-card/store-pay-record-list", params), // 流水列表
    storePayRecordAssociated: params => postAction("/customer_recharge/customer-recharge-card/store-pay-record-associated", params), // 流水关联
    payConfirm: params => postAction("/customer_recharge/customer-recharge-card/pay-confirm", params), // 收款确认
    storeFinish: params => postAction("/order/order-header/store-finish", params), // 门店订单完成
    storeViewForPrint: params => getAction("/order/order-header/store-view-for-print", params), // 门店订单详情-用于打印
    viewForPrint: params => getAction("/customer_recharge/customer-recharge-card/view-for-print", params), // 储值卡小票详情-用于打印
    barCode: params => postAction("/pay/payment/bar-code", params), // 流水号支付
    unionPayTagIndex: params => getAction("/pay/union-pay-tag/index", params),
    unionPayTagCreate: params => postAction("/pay/union-pay-tag/create", params),
    unionPayTagUpdate: params => postAction("/pay/union-pay-tag/update", params),
    unionPayTagSetStatus: params => postAction("/pay/union-pay-tag/set-status", params),
    unionPayTagSelect: params => getAction("/pay/union-pay-tag/select", params),
};

//门店工作台-开单收银
const goodsUnion = {
    goodsEnableTopCategoryList: params => getAction("/product-category/goods-enable-top-category-list", params), // 分类
    goodsUnionIndex: params => getAction("/view/goods-union/index", params) // 列表（包含服务、套餐、客装品)
};
//财务工作台-财务数据
const financial = {
    export: params => getAction("/data/financial-data/export", params) // 列表（包含服务、套餐、客装品)
};

// 其他
const otherRquestApi = {
    cityList: params => getAction("/promote/analysis/city-list", params), // 导出
};

// 短信模块
const sms = {
    send: params => postAction("/sms/send", params) // 短信验证码
};

//公共接口
const common = {
    approvalStatus: params => getAction("/common/common/approval-status", params), // 审批状态
};

// 文件上传
export const fileUpload = params => $fileUpload.putObject(params); // 上传文件
export const filedelete = params => $fileUpload.delete(params); // 删除文件

// 配置管理
const config = {
    getGroup: params => getAction("/config/get-group", params),
    create: params => postAction("/config/create", params),
    update: params => postAction("/config/update", params),
    view: params => getAction("/config/view", params),
    delete: params => deleteAction("/config/delete", params),
    getBrand: params => getAction("/brand/select", params)
};
// 配置管理
const memberAuthItem = {
    apps: params => getAction("/member/auth-item/apps", params),
    interfaceIndex: params => getAction("common/auth-interface/index", params)
};
// 链路数据导出
const linkDataExport = {
    reportDataExport: params => postAction("/promote/analysis/report-data-export", params), // 新10
    wecomReportExport: params => postAction("/promote/analysis/wecom-report-export", params), // 新16
    wecomReportCallbackExport: params => postAction("/promote/analysis/wecom-report-callback-export", params), // 新16上报回调
    wxcomCallbackExport: params => postAction("/promote/analysis/wxcom-callback-export", params), // 企微数据回调数据
    wecomCluesExport: params => postAction("/promote/analysis/wecom-clues-export", params), // 企微数据回调数据
    orderReportExport: params => postAction("/promote/analysis/order-report-export", params) // 下定上报
};

//角色管理
const addRole = params => postAction("/sys/role/add", params);
const editRole = params => putAction("/sys/role/edit", params);
const checkRoleCode = params => getAction("/sys/role/checkRoleCode", params);
const queryall = params => getAction("/sys/role/queryall", params);

//用户管理
const addUser = params => postAction("/sys/user/add", params);
const editUser = params => postAction("member/member/update", params);
const queryUserRole = params => getAction("/sys/user/queryUserRole", params);
const getUserList = params => getAction("/sys/user/list", params);
const frozenBatch = params => postAction("member/member/set-status", params);
const setSuperAdmin = params => postAction("member/member/set-admin", params);
const setPersonalPermissions = params => postAction("member/member/setUserItem", params);

//验证用户是否存在
const checkOnlyUser = params => getAction("/sys/user/checkOnlyUser", params);
//改变密码
const changePassword = params => putAction("/sys/user/changePassword", params);
//获取OSS签名
const getOSSSignature = params => getAction("/site/get-signature", params);
//上传图片信息
const siteImages = params => postAction("/site/images", params);

//权限管理
const addPermission = params => postAction("member/auth-item/create", params);
const editPermission = params => postAction("member/auth-item/update", params);
const getPermissionList = params => getAction("member/auth-item/index", params);
/*update_begin author:wuxianquan date:20190908 for:添加查询一级菜单和子菜单查询api */
const getSystemMenuList = params => getAction("/sys/permission/getSystemMenuList", params);
const getSystemSubmenu = params => getAction("/sys/permission/getSystemSubmenu", params);
const getSystemSubmenuBatch = params => getAction("/sys/permission/getSystemSubmenuBatch", params);

//查询角色树级列表
const queryTreeList = params => getAction("member/auth-item/view", params);
const queryTreeListForRole = params => getAction("member/auth-role/view", params);
const queryListAsync = params => getAction("/sys/permission/queryListAsync", params);
const queryRolePermission = params => getAction("/sys/permission/queryRolePermission", params);
const saveRolePermission = params => postAction("/sys/permission/saveRolePermission", params);
//左侧菜单栏
const queryPermissionsByUser = params => getAction("common/menu/left", params);
const loadAllRoleIds = params => getAction("/sys/permission/loadAllRoleIds", params);
const getPermissionRuleList = params => getAction("/sys/permission/getPermRuleListByPermId", params);
const queryPermissionRule = params => getAction("/sys/permission/queryPermissionRule", params);

//落地管理
const createPromotePage = params => postAction("promote-page/create", params);
const promotePageInfo = () => getAction("/sys/permission/queryPermissionRule", params);
const promotePageView = params => getAction("/promote-page/view", params);
const updatePromotePage = params => postAction("/promote-page/update", params);

//api管理
const queryApiList = params => getAction("/common/auth-interface/index", params);
const queryApiListInfo = params => getAction("common/auth-interface/view", params);
const editApiInfo = params => postAction("common/auth-interface/update", params);
const addApi = params => postAction("common/auth-interface/create", params);
const deleteApi = () => deleteAction("common/auth-interface/delete", params);

//企业管理
const changeEntity = params => postAction("member/member/change-entity", params);
const getEntity = params => getAction("/entity/index", params);
const entity = {
    create: params => postAction("/entity/create", params),
    view: params => getAction("/entity/view", params),
    update: params => postAction("/entity/update", params),
    delete: params => postAction("/entity/delete", params),
}

// 飞书多维表格配置
const feishuTableConfig = {
    index: params => getAction("/feishu/multidimensional-table-config/index", params),
    view: params => getAction("/feishu/multidimensional-table-config/view", params),
    create: params => postAction("/feishu/multidimensional-table-config/create", params),
    update: params => postAction("/feishu/multidimensional-table-config/update-data", params),
    types: params => getAction("/feishu/multidimensional-table-config/types", params),
    status: params => postAction("/feishu/multidimensional-table-config/status", params),
    subscribe: params => postAction("/feishu/multidimensional-table-config/subscribe", params),
    unsubscribe: params => postAction("/feishu/multidimensional-table-config/unsubscribe", params)
};

// 部门管理->角色管理
const queryDepartTreeList = params => getAction("member/auth-role/index", params);
const queryIdTree = params => getAction("member/auth-role/view", params);
const queryParentName = params => getAction("/sys/sysDepart/queryParentName", params);
const searchByKeywords = params => getAction("/sys/sysDepart/searchBy", params);
const deleteByDepartId = params => deleteAction("member/auth-role/delete", params);
const getDepartment = params => getAction("/procurement/dept-info", params);

//二级部门管理
const queryDepartPermission = params => getAction("/sys/permission/queryDepartPermission", params);
const saveDepartPermission = params => postAction("/sys/permission/saveDepartPermission", params);
const queryTreeListForDeptRole = params => getAction("/sys/sysDepartPermission/queryTreeListForDeptRole", params);
const queryDeptRolePermission = params => getAction("/sys/sysDepartPermission/queryDeptRolePermission", params);
const saveDeptRolePermission = params => postAction("/sys/sysDepartPermission/saveDeptRolePermission", params);
const queryMyDepartTreeList = params => getAction("/sys/sysDepart/queryMyDeptTreeList", params);

//字典标签专用（通过code获取字典数组）
export const ajaxGetDictItems = (code, params) => getAction(`/sys/dict/getDictItems/${code}`, params);

//从缓存中获取字典配置
function getDictItemsFromCache(dictCode) {
    if (Vue.ls.get(UI_CACHE_DB_DICT_DATA) &&
        Vue.ls.get(UI_CACHE_DB_DICT_DATA)[dictCode]) {
        let dictItems = Vue.ls.get(UI_CACHE_DB_DICT_DATA)[dictCode];
        console.log("-----------getDictItemsFromCache----------dictCode=" +
            dictCode +
            "---- dictItems=",
            dictItems
        );
        return dictItems;
    }
}

// 中转HTTP请求
export const transitRESTful = {
    get: (url, parameter) => getAction(getTransitURL(url), parameter),
    post: (url, parameter) => postAction(getTransitURL(url), parameter),
    put: (url, parameter) => putAction(getTransitURL(url), parameter),
    http: (url, parameter) => httpAction(getTransitURL(url), parameter)
};

// 广告素材标签管理
const adsMaterialLabel = {
    getAll: params => getAction("/promote/ads-material-label/get-all", params),
    batchUpdate: params => postAction("/promote/ads-material-label/batch-update", params)
};

// 系统标签管理
const adsCusLabel = {
    index: params => getAction("/promote/ads-cus-label/index", params),
    create: params => postAction("/promote/ads-cus-label/create", params),
    updateData: params => postAction("/promote/ads-cus-label/update-data", params),
    status: params => postAction("/promote/ads-cus-label/status", params),
    select: params => getAction("/promote/ads-cus-label/select", params),
    getAll: params => getAction("/promote/ads-cus-label/get-all", params)
};

// 退款申请管理
const refundApplicationApi = {
    list: params => getAction("/order/refund-application/index", params),
    view: params => getAction("/order/refund-application/view", params),
    create: params => postAction("/order/refund-application/create", params),
    update: params => postAction('/order/refund-application/update', params),
    cancel: id => postAction(`/order/refund-application/${id}/cancel`, {}),
    resubmit: (id, params) => postAction(`/order/refund-application/${id}/resubmit`, params),
    export: params => getAction("/order/refund-application/export", params),
    statusSelectList: params => getAction("/order/refund-application/status-select-list", params),
    refundableOrders: params => getAction("/order/refund-application/refundable-orders", params)
};

// 广告预算
const adsAdvertisingBudget = {
    index: params => getAction("/promote/ads-advertising-budget/index", params),
    updateData: params => postAction("/promote/ads-advertising-budget/update", params),
    delete: params => deleteAction("/promote/ads-advertising-budget/delete", params),
};

// 存量客资
const customerExistingData = {
    index: params => getAction("/customer/existing-data/index", params),
    export: params => getAction("/customer/existing-data/export", params),
    view: params => getAction("/customer/existing-data/view", params),
    update: params => postAction("/customer/existing-data/update", params),
    setStatus: params => postAction("/customer/existing-data/set-status", params),
    mobileCheck: params => getAction("/customer/existing-data/mobile-check", params),
    logList: params => getAction("/customer/existing-data-log/index", params),
    createLog: params => postAction("/customer/existing-data-log/create", params),
}

export {
    addRole,
    editRole,
    checkRoleCode,
    addUser,
    editUser,
    queryUserRole,
    getUserList,
    queryall,
    frozenBatch,
    checkOnlyUser,
    changePassword,
    getPermissionList,
    addPermission,
    editPermission,
    queryTreeList,
    queryListAsync,
    queryRolePermission,
    saveRolePermission,
    queryPermissionsByUser,
    loadAllRoleIds,
    getPermissionRuleList,
    queryPermissionRule,
    queryDepartTreeList,
    queryIdTree,
    queryParentName,
    searchByKeywords,
    deleteByDepartId,
    getDepartment,
    queryTreeListForRole,
    getSystemMenuList,
    getSystemSubmenu,
    getSystemSubmenuBatch,
    queryDepartPermission,
    saveDepartPermission,
    queryTreeListForDeptRole,
    queryDeptRolePermission,
    saveDeptRolePermission,
    queryMyDepartTreeList,
    getDictItemsFromCache,
    queryApiList,
    queryApiListInfo,
    addApi,
    editApiInfo,
    deleteApi,
    setSuperAdmin,
    setPersonalPermissions,
    changeEntity,
    getOSSSignature,
    createPromotePage,
    promotePageView,
    updatePromotePage,
    getEntity,
    storeManage,
    goodsCategory,
    productManage,
    promoteAccount,
    promoteManage,
    promoteManageAccount,
    storePerformanceAnalysis,
    promoteLink,
    promoteProject,
    promoteAgent,
    materialManage,
    wechatAccount,
    supplierManage,
    inventoryManage,
    departmentManage,
    fixedAssetManage,
    roleList,
    department,
    reportedLoss,
    consumption,
    adsMaterial,
    DataAnalysis,
    landPageList,
    accountCheck,
    requestpayout,
    landPageh5,
    Applet,
    accountpullrecord,
    config,
    cusQrgroup,
    cusQrcode,
    cususer,
    com,
    channel,
    cusCount,
    wxcomUser,
    wxcomDepartment,
    wxcomMessage,
    useTag,
    goodTag,
    landPageGroup,
    linkDataExport,
    goodPackage,
    goodProduct,
    productMaterial,
    storedValueCard,
    customerService,
    customerCount,
    theOrderManagementByorder,
    mockApi,
    PublicApi,
    memberAuthItem,
    customer,
    refund,
    refundReason,
    dashboard,
    direction,
    target,
    qrcodebgi,
    unionPay,
    wechatAccount_wct,
    wechatAllot,
    fansRecord,
    runningWater,
    wechatServicer,
    subscriptionService,
    subscriptionProject,
    storeStatistics,
    inventoryStatistics,
    bindCode,
    cusExtend,
    otherRquestApi,
    payRecord,
    cusDynamicQrcode,
    penalty,
    territorialSort,
    cusDynamicQrcodeCount,
    wxcomDeploy,
    provider,
    suite,
    authCom,
    staffRoster,
    getAppid,
    addFansAnalysis,
    depStoreAnalysis,
    orderStore,
    cardsSales,
    storeGoods,
    goodsUnion,
    orderType,
    teacherType,
    payCode,
    dataSource,
    lklConfig,
    complaint,
    gift,
    wxcomAdd,
    fanStoreAnalysis,
    addFansCityAnalysis,
    fanStoreCityAnalysis,
    teacherCusAnalysis,
    teacherAnalysis,
    servicerAnalysis,
    projectGrounp,
    promoteMainBody,
    brand,
    customerIntroduced,
    sms,
    financial,
    teacherTypeModal,
    entity,
    siteImages,
    common,
    adsCusLabel,
    adsMaterialLabel,
    feishuTableConfig,
    refundApplicationApi,
    mobileViewLog,
    customerFeedback,
    adsAdvertisingBudget,
    customerExistingData,
    promoteManageLocalAccount,
    adsLocalClueList
};
