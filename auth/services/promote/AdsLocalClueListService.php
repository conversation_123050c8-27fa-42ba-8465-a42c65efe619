<?php

namespace auth\services\promote;

use Exception;
use common\helpers\ArrayHelper;
use auth\models\promote\AdsLocalClueList;
use common\services\promote\AdsLocalClueListService as CommonAdsLocalClueListService;
use common\helpers\DateHelper;
use common\models\promote\AdsLocalAccountSub;
use common\models\backendapi\PromoteChannel;
use common\helpers\ResultHelper;
use common\enums\promote\EffectiveStateEnum;
use common\models\common\DepartmentAssignment;
use services\UserService;
use common\components\report\LocalReport;
use common\components\report\ReportFactory;
use Yii;

class AdsLocalClueListService extends CommonAdsLocalClueListService
{
    /**
     * @var AdsLocalClueList
     */
    public static $modelClass = AdsLocalClueList::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = static::$modelClass::find()
            ->alias('acl')
            ->select([
                'acl.id',
                'acl.clue_id',
                'acl.name',
                'acl.mobile',
                'acl.effective_state',
                'acl.allocation_user_id',
                'acl.allocation_user_name',
                'acl.call_num',
                'acl.clue_created_at',
                'acl.last_revisit_time',
                'acl.material_id',
                'acl.adid',
                'acl.created_at',
                'pc.name as promote_name',
                'als.sub_advertiser_name as account_name',
                'als.sub_advertiser_id'
            ])
            ->leftJoin(['pc' => PromoteChannel::tableName()], 'pc.id = acl.promote_id')
            ->leftJoin(['als' => AdsLocalAccountSub::tableName()], 'als.id = acl.local_account_sub_id');

        $query->where(['acl.entity_id' => UserService::getInst()->current_entity_id])
            ->andFilterWhere(['like', 'acl.mobile', trim($params['mobile'])])
            ->andFilterWhere(['acl.allocation_user_id' => $params['allocation_user_id']])
            ->andFilterWhere(['between', 'acl.clue_created_at', $params['clue_created_at_start'], $params['clue_created_at_end']])
            ->andFilterWhere(['between', 'acl.last_revisit_time', $params['last_revisit_time_start'], $params['last_revisit_time_end']])
            ->andFilterWhere(['acl.effective_state' => $params['effective_state']]);

        // 素材ID/客户ID/计划ID搜索（支持多个字段的模糊搜索）
        if (!empty($params['material_search'])) {
            $searchTerm = trim($params['material_search']);
            $query->andWhere([
                'or',
                ['like', 'acl.material_id', $searchTerm],
                ['like', 'acl.clue_id', $searchTerm],
                ['like', 'acl.adid', $searchTerm],
            ]);
        }

        return static::authHandle($query);
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
        ]);

        $query = static::getQuery($params);

        $totalCount = $query->count();
        if (!$totalCount) {
            return [[], 0];
        }

        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 20);   //条数
        $offset = ($page - 1) * $limit;

        $query->offset($offset)->limit($limit)->orderBy(['acl.id' => SORT_DESC]);
        $list = $query->asArray()->all();

        foreach ($list as &$item) {
            $item['mobile'] = ResultHelper::mobileEncryption($item['mobile']);
            $item['effective_state_text'] = EffectiveStateEnum::getValue($item['effective_state']);
            $item['clue_created_at_text'] = DateHelper::toDate($item['clue_created_at']);
            $item['last_revisit_time_text'] = DateHelper::toDate($item['last_revisit_time']);
            $item['created_at_text'] = DateHelper::toDate($item['created_at']);

            $item['customer_info'] = [
                'name' => $item['name'] ?: '未知',
                'mobile' => $item['mobile']
            ];
        }

        return [$list, $totalCount];
    }

    public static function setStatus($id)
    {
        $model = static::$modelClass::findOne($id);
        if (!$model) {
            throw new Exception('数据不存在');
        }

        $effectiveState = Yii::$app->request->post('effective_state');

        if (isset($model->scenarios()['set_effective_state'])) {
            $model->scenario = 'set_effective_state';
        }
        $model->effective_state = $effectiveState;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrMsg());
        }

        if (in_array($effectiveState, [EffectiveStateEnum::ADD_WEIXIN, EffectiveStateEnum::INVALID])) {
            $reportService = ReportFactory::getReportServiceByChannelId($model->promote_id);
            /** @var LocalReport $reportService */
            $reportService->changeEffectiveStateReport($model);
        }

        return true;
    }

    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        return EffectiveStateEnum::getSelectList();
    }

    public static function authHandle($query)
    {
        $title = Yii::$app->services->rbacAuthRole->getTitle();
        if (Yii::$app->services->auth->isSuperAdmin() || in_array('销售客服经理', $title)) {
            return $query;
        }

        $getScope = Yii::$app->services->scopeDataService->getScope();
        $query->leftJoin(['da' => DepartmentAssignment::tableName()], 'da.user_id = acl.allocation_user_id');
        $query->andFilterWhere([
            'or',
            ['da.dept_id' => $getScope],
            // ['s.dept_id' => $getScope],
            ['acl.allocation_user_id' => UserService::getInst()->id]
        ]);
        return $query;
    }
}
