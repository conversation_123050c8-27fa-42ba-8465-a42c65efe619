<?php

namespace auth\services\promote;

use Exception;
use common\helpers\ArrayHelper;
use auth\models\promote\AdsLocalAccountSub;
use common\services\promote\AdsLocalAccountSubService as CommonAdsLocalAccountSubService;

class AdsLocalAccountSubService extends CommonAdsLocalAccountSubService
{
    /**
     * @var AdsLocalAccountSub
     */
    public static $modelClass = AdsLocalAccountSub::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = parent::getQuery($params);
        $query->andFilterWhere(['status' => $params['status']]);
        if ($params['keyword']) {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
        ]);

        $query = static::getQuery($params);
        $query->with(['createdPerson', 'updatedPerson']);

        $totalCount = $query->count();
        $list = $query->all();

        return [$list, $totalCount];
    }


    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    /**
     * 新增
     *
     * @param array $params
     * @return AdsLocalAccountSub
     */
    public static function create($params)
    {
        $model = new static::$modelClass();
        $model->scenario = 'manual_edit_create'; // 手动创建账号场景
        $model->attributes = $params;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrMsg());
        }
        return $model;
    }

    /**
     * 修改
     *
     * @return bool
     */
    public static function update($id, $params)
    {
        $model = static::getInfoById($id);
        $model->scenario = 'manual_edit_create'; // 手动编辑场景
        $model->attributes = $params;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrMsg());
        }
        return true;
    }
}
