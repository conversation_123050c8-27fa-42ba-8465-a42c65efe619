<?php

use common\components\migrate\Migration;

/**
 * Class m250731_085442_add_local_clue_table
 */
class m250731_085442_add_local_clue_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            CREATE TABLE `erp_ads_local_account_sub` (
                `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                `td_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '广告主账户id,ads_account表ID',
                `sub_advertiser_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '账户名称' COLLATE 'utf8mb4_general_ci',
                `sub_advertiser_id` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '账户id' COLLATE 'utf8mb4_general_ci',
                `promote_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '渠道ID,promote_channel表ID',
                `link_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '链路ID',
                `project_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '项目ID',
                `direction_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '定向ID',
                `responsible_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '责任人ID',
                `agent_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '代理商ID',
                `rebates` DECIMAL(10,3) NOT NULL DEFAULT '0.000' COMMENT '返点数',
                `way` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '录入类型：1 自动 2 手动',
                `code` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '推广代号' COLLATE 'utf8_general_ci',
                `status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用、1启用、2备用',
                `main_body_id` INT(11) NOT NULL DEFAULT '0' COMMENT '主体ID,ads_main_body表ID',
                `dept_id` INT(11) NOT NULL DEFAULT '0' COMMENT '部门ID',
                `entity_id` INT(11) NOT NULL DEFAULT '0' COMMENT '企业ID',
                `created_by` INT(11) NOT NULL DEFAULT '0' COMMENT '创建人',
                `updated_by` INT(11) NOT NULL DEFAULT '0' COMMENT '更新人',
                `created_at` INT(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
                `updated_at` INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
                PRIMARY KEY (`id`) USING BTREE,
                KEY `td_id` (`td_id`) USING BTREE,
                KEY `subAdvertiserId_entityId` (`sub_advertiser_id`,`entity_id`) USING BTREE
            ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='广告本地推子账户表';
        ";
        $this->execute($sql);

        $sql = "
            CREATE TABLE `erp_ads_local_clue_list` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `local_account_sub_id` int(11) NOT NULL DEFAULT '0' COMMENT '本地推子账户表ID,ads_local_account_sub表ID',
                `clue_id` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '线索ID' COLLATE 'utf8_general_ci',
                `name` varchar(32) NOT NULL DEFAULT '' COMMENT '客户姓名',
                `mobile` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '手机号',
                `promote_id` int(11) NOT NULL DEFAULT '0' COMMENT '渠道ID,promote_channel表ID',
                `system_tag` varchar(100) NOT NULL DEFAULT '' COMMENT '系统标签ids，wxcom_cus_tag表中ID',
                `effective_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线索阶段',
                `allocation_user_id` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分配人员ID，backend_member表ID',
                `allocation_user_name` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '分配人员,平台分配人员',
                `call_num` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拨打次数',
                `clue_created_at` int(11) NOT NULL DEFAULT '0' COMMENT '线索创建时间',
                `last_revisit_time` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最近回访时间',
                `material_id` varchar(32) NOT NULL DEFAULT '' COMMENT '素材ID',
                `adid` varchar(100) NOT NULL DEFAULT '' COMMENT '计划id',
                `entity_id` int(11) NOT NULL DEFAULT '0' COMMENT '企业ID',
                `created_by` int(11) NOT NULL DEFAULT '0' COMMENT '创建人',
                `updated_by` int(11) NOT NULL DEFAULT '0' COMMENT '更新人',
                `created_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
                `updated_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
                PRIMARY KEY (`id`) USING BTREE,
                KEY `local_account_sub_id` (`local_account_sub_id`) USING BTREE,
                UNIQUE INDEX `clue_id` (`clue_id`)
                ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='广告本地推线索列表';
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250731_085442_add_local_clue_table cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250731_085442_add_local_clue_table cannot be reverted.\n";

        return false;
    }
    */
}
