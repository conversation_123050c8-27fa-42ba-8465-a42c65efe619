<?php

namespace common\components\report;

use common\helpers\Tool;
use common\models\backend\order\OrderHeader;
use common\models\common\AdsAccount;
use common\models\Customer;
use common\models\promote\AdsLocalAccountSub;
use common\models\promote\AdsLocalClueList;
use common\enums\log\AdsReportTypeEnum;
use common\enums\promote\EffectiveStateEnum;
use Yii;

/**
 * 本地推
 */
class LocalReport extends TiktokReport
{
    private $accessToken;
    protected $mobile;
    protected $accountId;
    protected $clueId;
    protected $clueInfo;
    protected $params;

    public function orderCreatedReport(OrderHeader $order)
    {
        $this->order = $order;

        if (!$this->before()) {
            return false;
        }
        $res = $this->report(['clue_convert_state' => 'CLUE_HIGH_INTENTION']);
        if (!$this->after(AdsReportTypeEnum::ORDER_CREATED, $res)) {
            return false;
        }

        return true;
    }

    public function changeEffectiveStateReport(AdsLocalClueList $model)
    {
        $this->clueInfo = $model;

        if (!$this->before()) {
            return false;
        }

        switch ($model->effective_state) {
            case EffectiveStateEnum::ADD_WEIXIN:
                $this->params = ['clue_convert_state' => 'CLUE_CONFIRM'];
                break;
            case EffectiveStateEnum::INVALID:
                $eventData = new \stdClass();
                $eventData->reason_code = 'OTHER'; 
                $eventData->reason_message = '';
                $this->params = ['clue_convert_state' => 'INVALID_EVENT', 'event_data' => $eventData];
                break;
            default:
                return false;
        }

        $res = $this->report($this->params);
        if (!$this->after(AdsReportTypeEnum::CLUE_CHANGE_EFFECTIVE_STATE, $res)) {
            return false;
        }

        return true;
    }

    public function before()
    {
        if (!isset($this->clueInfo)) {
            $customer = Customer::findOne($this->order->cus_id);
            if (!$customer || !$customer->mobile) {
                return false;
            }
            $this->mobile = $customer->mobile;
        } else {
            $this->mobile = $this->clueInfo->mobile;
        }

        $clueInfo = AdsLocalClueList::find()
            ->alias('cl')
            ->select(['as.sub_advertiser_id', 'cl.clue_id', 'aa.access_token'])
            ->leftJoin(['as' => AdsLocalAccountSub::tableName()], 'as.id = cl.local_account_sub_id')
            ->leftJoin(['aa' => AdsAccount::tableName()], 'aa.id = as.td_id')
            ->where(['cl.mobile' => $this->mobile])
            ->asArray()
            ->one();
        if (!$clueInfo) {
            return false;
        }

        $this->accountId = $clueInfo['sub_advertiser_id'];
        $this->clueId = $clueInfo['clue_id'];
        $this->accessToken = $clueInfo['access_token'];

        if (empty($this->accessToken) || empty($this->accountId) || empty($this->clueId)) {
            return false;
        }

        return true;
    }

    public function after($type, $result)
    {
        if (!$result || $result['code'] != 0) {
            Yii::error('本地推下订上报失败: ' . json_encode($result, JSON_UNESCAPED_UNICODE), 'LocalReport::afterOrderCreatedReport');
            return false;
        }

        $this->saveReportLog($type, $result, $this->params);

        if ($type != AdsReportTypeEnum::ORDER_CREATED) {
            return true;
        }

        $res = AdsLocalClueList::updateAll(
            ['effective_state' => EffectiveStateEnum::CONVERTED],
            ['clue_id' => $this->clueId]
        );

        return true;
    }

    /**
     * 上报
     * 文档：https://open.oceanengine.com/labels/37/docs/****************
     *
     * @param array $params
     * @return mixed
     */
    public function report(array $params)
    {
        $url = "https://api.oceanengine.com/open_api/2/tools/clue/life/callback/";

        $header = [
            "Access-Token: " . $this->accessToken
        ];

        $params['local_account_ids'] = [$this->accountId];
        $params['clue_id'] = $this->clueId;
        $this->params = $params;

        return Tool::curlRequest($url, $params, true, 10, $header);
    }
}
