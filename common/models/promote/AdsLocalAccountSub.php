<?php

namespace common\models\promote;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%ads_local_account_sub}}".
 *
 * @property int $id id
 * @property int $td_id 广告主账户id,ads_account表ID
 * @property string $sub_advertiser_name 账户名称
 * @property string $sub_advertiser_id 账户id
 * @property int $promote_id 渠道ID,promote_channel表ID
 * @property int $link_id 链路ID
 * @property int $project_id 项目ID
 * @property int $direction_id 定向ID
 * @property int $responsible_id 责任人ID
 * @property int $agent_id 代理商ID
 * @property double $rebates 返点数
 * @property int $way 录入类型：1 自动 2 手动
 * @property string $code 推广代号
 * @property int $status 状态：0禁用、1启用、2备用
 * @property int $main_body_id 主体ID,ads_main_body表ID
 * @property int $dept_id 部门ID
 * @property int $entity_id 企业ID
 * @property int $created_by 创建人
 * @property int $updated_by 更新人
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 */
class AdsLocalAccountSub extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%ads_local_account_sub}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sub_advertiser_name'], 'string', 'max' => 255],
            [['sub_advertiser_id'], 'string', 'max' => 255],
            [['code'], 'string', 'max' => 20],
            [['direction_id'], 'integer', 'min' => 0],
            [['td_id', 'sub_advertiser_name', 'sub_advertiser_id', 'promote_id', 'agent_id', 'rebates', 'status', 'main_body_id', 'dept_id'], 'required'],
            [['td_id', 'promote_id', 'link_id', 'project_id', 'direction_id', 'responsible_id', 'agent_id', 'way', 'status', 'main_body_id', 'dept_id'], 'integer'],
            [['rebates'], 'double'],
            [['sub_advertiser_name', 'sub_advertiser_id', 'code'], 'trim'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'id',
            'td_id' => '广告主账户id,ads_account表ID',
            'sub_advertiser_name' => '账户名称',
            'sub_advertiser_id' => '账户id',
            'promote_id' => '渠道ID,promote_channel表ID',
            'link_id' => '链路ID',
            'project_id' => '项目ID',
            'direction_id' => '定向ID',
            'responsible_id' => '责任人ID',
            'agent_id' => '代理商ID',
            'rebates' => '返点数',
            'way' => '录入类型',
            'code' => '推广代号',
            'status' => '状态',
            'main_body_id' => '主体ID,ads_main_body表ID',
            'dept_id' => '部门ID',
            'entity_id' => '企业ID',
            'created_by' => '创建人',
            'updated_by' => '更新人',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }

    /**
     * 场景
     * default 默认场景；
     * auto_create 自动拉创建账号；
     *
     * @return array
     */
    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['td_id', 'sub_advertiser_name', 'sub_advertiser_id', 'promote_id', 'link_id', 'project_id', 'direction_id', 'responsible_id', 'agent_id', 'rebates', 'way', 'status', 'main_body_id', 'dept_id', 'code'],
            'auto_create' => ['td_id', 'status', 'sub_advertiser_name', 'sub_advertiser_id', 'way', 'main_body_id'],
            'manual_edit_create' => ['sub_advertiser_name', 'sub_advertiser_id', 'responsible_id', 'promote_id', 'main_body_id', 'link_id', 'project_id', 'direction_id', 'status', 'code'],
            'auto_edit_create' => ['responsible_id', 'link_id', 'project_id', 'direction_id', 'status', 'report_event', 'main_body_id', 'code'],
            'status' => ['status'],
            'set_agent' => ['agent_id'],
            'set_rebates' => ['rebates'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_by = UserService::getInst()->id;
            $this->created_at = time();
            $this->entity_id = UserService::getInst()->current_entity_id;
        }
        $this->updated_by = UserService::getInst()->id;
        $this->updated_at = time();

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'created_by']);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    public function getUpdatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'updated_by']);
    }
}
